package com.ctrip.dcs.domain.schedule.sort.hierarchical.grade;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortGrade;
import lombok.Builder;
import lombok.Getter;

import java.util.Map;

/**
 * 大类分数类
 * 存储单个大类的分数、等级和详细信息
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Builder
public class CategoryScore {
    
    /**
     * 大类类型
     */
    private SortCategory category;
    
    /**
     * 原始分数 [0, 100]
     */
    private double rawScore;
    
    /**
     * 等级
     */
    private SortGrade grade;
    
    /**
     * 权重
     */
    private double weight;
    
    /**
     * 加权分数 (rawScore * weight)
     */
    private double weightedScore;
    
    /**
     * GPA值 (等级对应的GPA)
     */
    private double gpaValue;
    
    /**
     * 加权GPA (gpaValue * weight)
     */
    private double weightedGpa;
    
    /**
     * 子项分数详情
     * key: 特征项ID
     * value: 特征项分数
     */
    private Map<String, Double> featureScores;
    
    /**
     * 子项权重详情
     * key: 特征项ID
     * value: 特征项权重
     */
    private Map<String, Double> featureWeights;
    
    /**
     * 是否计算成功
     */
    @Builder.Default
    private boolean successful = true;
    
    /**
     * 错误信息（如果计算失败）
     */
    private String errorMessage;
    
    /**
     * 计算时间戳
     */
    @Builder.Default
    private long timestamp = System.currentTimeMillis();
    
    /**
     * 创建失败的大类分数
     * @param category 大类
     * @param errorMessage 错误信息
     * @return 失败的大类分数
     */
    public static CategoryScore createFailed(SortCategory category, String errorMessage) {
        return CategoryScore.builder()
                .category(category)
                .rawScore(0.0)
                .grade(SortGrade.D)
                .weight(0.0)
                .weightedScore(0.0)
                .gpaValue(SortGrade.D.getGpaValue())
                .weightedGpa(0.0)
                .successful(false)
                .errorMessage(errorMessage)
                .build();
    }
    
    /**
     * 创建默认的大类分数（用于降级场景）
     * @param category 大类
     * @param weight 权重
     * @return 默认大类分数
     */
    public static CategoryScore createDefault(SortCategory category, double weight) {
        return CategoryScore.builder()
                .category(category)
                .rawScore(50.0) // 默认C级分数
                .grade(SortGrade.C)
                .weight(weight)
                .weightedScore(50.0 * weight)
                .gpaValue(SortGrade.C.getGpaValue())
                .weightedGpa(SortGrade.C.getGpaValue() * weight)
                .successful(true)
                .build();
    }
    
    /**
     * 获取大类描述信息
     * @return 描述字符串
     */
    public String getDescription() {
        return String.format("%s: %.2f分 (%s级, 权重%.2f)", 
                category.getDescription(), rawScore, grade.getCode(), weight);
    }
    
    /**
     * 判断是否为优秀等级
     * @return true if A级
     */
    public boolean isExcellent() {
        return grade == SortGrade.A;
    }
    
    /**
     * 判断是否为较差等级
     * @return true if D级
     */
    public boolean isPoor() {
        return grade == SortGrade.D;
    }
    
    /**
     * 获取子项数量
     * @return 子项数量
     */
    public int getFeatureCount() {
        return featureScores != null ? featureScores.size() : 0;
    }
    
    /**
     * 获取指定特征项的分数
     * @param featureId 特征项ID
     * @return 分数，如果不存在返回0.0
     */
    public double getFeatureScore(String featureId) {
        return featureScores != null ? featureScores.getOrDefault(featureId, 0.0) : 0.0;
    }
    
    /**
     * 获取指定特征项的权重
     * @param featureId 特征项ID
     * @return 权重，如果不存在返回0.0
     */
    public double getFeatureWeight(String featureId) {
        return featureWeights != null ? featureWeights.getOrDefault(featureId, 0.0) : 0.0;
    }
}
