package com.ctrip.dcs.domain.schedule.sort.hierarchical.monitor;

import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.HierarchicalSortConfig;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;

import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 分层排序监控器
 * 负责收集和记录分层排序的各种监控指标
 * 
 * <AUTHOR> Assistant
 */
public class HierarchicalSortMonitor {
    
    private static final Logger logger = LoggerFactory.getLogger(HierarchicalSortMonitor.class);
    
    private final HierarchicalSortConfig config;
    
    // 计数器
    private final AtomicLong totalSortCount = new AtomicLong(0);
    private final AtomicLong successfulSortCount = new AtomicLong(0);
    private final AtomicLong fallbackCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    
    // 性能指标
    private final AtomicLong totalProcessingTime = new AtomicLong(0);
    private final AtomicLong maxProcessingTime = new AtomicLong(0);
    private final AtomicLong totalDriverCount = new AtomicLong(0);
    
    // 状态信息
    private final AtomicReference<String> lastError = new AtomicReference<>();
    private final AtomicLong lastErrorTime = new AtomicLong(0);
    private final AtomicLong lastSuccessTime = new AtomicLong(0);
    
    public HierarchicalSortMonitor(HierarchicalSortConfig config) {
        this.config = config;
    }
    
    /**
     * 记录排序指标
     * @param driverCount 司机数量
     * @param processingTime 处理时间（毫秒）
     * @param usedFallback 是否使用了降级
     */
    public void recordSortingMetrics(int driverCount, long processingTime, boolean usedFallback) {
        totalSortCount.incrementAndGet();
        totalDriverCount.addAndGet(driverCount);
        totalProcessingTime.addAndGet(processingTime);
        
        // 更新最大处理时间
        long currentMax = maxProcessingTime.get();
        while (processingTime > currentMax && !maxProcessingTime.compareAndSet(currentMax, processingTime)) {
            currentMax = maxProcessingTime.get();
        }
        
        if (usedFallback) {
            fallbackCount.incrementAndGet();
        } else {
            successfulSortCount.incrementAndGet();
            lastSuccessTime.set(System.currentTimeMillis());
        }
        
        // 记录监控日志
        if (config.isMonitoringEnabled()) {
            logger.info("HierarchicalSort_Metrics", 
                    String.format("driverCount=%d, processingTime=%d, usedFallback=%s, totalSorts=%d", 
                            driverCount, processingTime, usedFallback, totalSortCount.get()));
        }
    }
    
    /**
     * 记录降级事件
     * @param reason 降级原因
     * @param context 排序上下文
     */
    public void recordFallbackEvent(String reason, SortContext context) {
        fallbackCount.incrementAndGet();
        lastError.set(reason);
        lastErrorTime.set(System.currentTimeMillis());
        
        logger.warn("HierarchicalSort_Fallback", 
                String.format("分层排序降级 - 原因: %s, 订单ID: %s", 
                        reason, context.getDspOrder().getDspOrderId()));
    }
    
    /**
     * 记录错误事件
     * @param error 错误信息
     * @param context 排序上下文
     */
    public void recordError(String error, SortContext context) {
        errorCount.incrementAndGet();
        lastError.set(error);
        lastErrorTime.set(System.currentTimeMillis());
        
        logger.error("HierarchicalSort_Error", 
                String.format("分层排序错误 - 错误: %s, 订单ID: %s", 
                        error, context.getDspOrder().getDspOrderId()));
    }
    
    /**
     * 获取成功率
     * @return 成功率 [0.0, 1.0]
     */
    public double getSuccessRate() {
        long total = totalSortCount.get();
        return total > 0 ? (double) successfulSortCount.get() / total : 0.0;
    }
    
    /**
     * 获取降级率
     * @return 降级率 [0.0, 1.0]
     */
    public double getFallbackRate() {
        long total = totalSortCount.get();
        return total > 0 ? (double) fallbackCount.get() / total : 0.0;
    }
    
    /**
     * 获取平均处理时间
     * @return 平均处理时间（毫秒）
     */
    public double getAverageProcessingTime() {
        long total = totalSortCount.get();
        return total > 0 ? (double) totalProcessingTime.get() / total : 0.0;
    }
    
    /**
     * 获取平均司机数量
     * @return 平均司机数量
     */
    public double getAverageDriverCount() {
        long total = totalSortCount.get();
        return total > 0 ? (double) totalDriverCount.get() / total : 0.0;
    }
    
    /**
     * 检查监控器健康状态
     * @return true if 健康
     */
    public boolean isHealthy() {
        // 检查成功率是否在可接受范围内
        double successRate = getSuccessRate();
        if (successRate < (1.0 - config.getFallbackThreshold())) {
            return false;
        }
        
        // 检查是否有最近的成功记录
        long now = System.currentTimeMillis();
        long lastSuccess = lastSuccessTime.get();
        if (lastSuccess > 0 && (now - lastSuccess) > 300000) { // 5分钟内没有成功记录
            return false;
        }
        
        return true;
    }
    
    /**
     * 重置监控指标
     */
    public void reset() {
        totalSortCount.set(0);
        successfulSortCount.set(0);
        fallbackCount.set(0);
        errorCount.set(0);
        totalProcessingTime.set(0);
        maxProcessingTime.set(0);
        totalDriverCount.set(0);
        lastError.set(null);
        lastErrorTime.set(0);
        lastSuccessTime.set(0);
        
        logger.info("HierarchicalSort_Monitor", "监控指标已重置");
    }
    
    /**
     * 获取监控报告
     * @return 监控报告字符串
     */
    public String getMonitoringReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 分层排序监控报告 ===\n");
        report.append(String.format("总排序次数: %d\n", totalSortCount.get()));
        report.append(String.format("成功次数: %d\n", successfulSortCount.get()));
        report.append(String.format("降级次数: %d\n", fallbackCount.get()));
        report.append(String.format("错误次数: %d\n", errorCount.get()));
        report.append(String.format("成功率: %.2f%%\n", getSuccessRate() * 100));
        report.append(String.format("降级率: %.2f%%\n", getFallbackRate() * 100));
        report.append(String.format("平均处理时间: %.2f ms\n", getAverageProcessingTime()));
        report.append(String.format("最大处理时间: %d ms\n", maxProcessingTime.get()));
        report.append(String.format("平均司机数量: %.2f\n", getAverageDriverCount()));
        report.append(String.format("健康状态: %s\n", isHealthy() ? "健康" : "异常"));
        
        String lastErr = lastError.get();
        if (lastErr != null) {
            report.append(String.format("最后错误: %s (时间: %d)\n", lastErr, lastErrorTime.get()));
        }
        
        return report.toString();
    }
    
    /**
     * 获取简要状态
     * @return 状态字符串
     */
    public String getStatus() {
        return String.format("HierarchicalSortMonitor[total=%d, success=%.2f%%, fallback=%.2f%%, healthy=%s]", 
                totalSortCount.get(), getSuccessRate() * 100, getFallbackRate() * 100, isHealthy());
    }
    
    // Getter方法
    public long getTotalSortCount() { return totalSortCount.get(); }
    public long getSuccessfulSortCount() { return successfulSortCount.get(); }
    public long getFallbackCount() { return fallbackCount.get(); }
    public long getErrorCount() { return errorCount.get(); }
    public long getMaxProcessingTime() { return maxProcessingTime.get(); }
    public String getLastError() { return lastError.get(); }
    public long getLastErrorTime() { return lastErrorTime.get(); }
    public long getLastSuccessTime() { return lastSuccessTime.get(); }
}
