package com.ctrip.dcs.domain.schedule.sort;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.schedule.sort.feature.Feature;
import com.ctrip.dcs.domain.schedule.sort.score.Scorer;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 打分器
 * <AUTHOR>
 */
public class Sorter {

    private Scorer scorer;

    private List<Feature> features;

    public Sorter(Scorer scorer) {
        Assert.notNull(scorer);
        this.scorer = scorer;
        this.features = Lists.newArrayList();
    }

    /**
     * 添加特征项
     * @param feature 特征项
     */
    public void addFeature(Feature feature) {
        Assert.notNull(feature);
        features.add(feature);
    }

    /**
     * 排序打分
     * @param models
     * @param context
     * @return
     */
    public List<SortModel> sort(List<SortModel> models, SortContext context) {
        if (CollectionUtils.isEmpty(models)) {
            return Collections.emptyList();
        }
        for (Feature feature : features) {
            // 计算特征值
            feature.value(models, context);
        }
        for (SortModel model : models) {
            // 计算总分
            model.score(scorer);
            //记录到ck
            context.record(model);
        }
        // 排序，总分从高到低
        models.sort(Comparator.comparing(SortModel::getScore).reversed());
        return models;
    }

}
