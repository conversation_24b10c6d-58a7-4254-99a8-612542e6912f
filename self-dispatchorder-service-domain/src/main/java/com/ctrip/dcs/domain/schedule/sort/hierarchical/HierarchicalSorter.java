package com.ctrip.dcs.domain.schedule.sort.hierarchical;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.Sorter;
import com.ctrip.dcs.domain.schedule.sort.feature.Feature;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.category.HierarchicalFeature;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.HierarchicalSortConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.monitor.HierarchicalSortMonitor;
import com.ctrip.dcs.domain.schedule.sort.score.Scorer;
import com.ctrip.dcs.domain.schedule.sort.score.WeightScorer;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.base.Stopwatch;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 分层排序器
 * 基于四大类（时空效率、服务质量、订单匹配度、全局效率）的分层排序实现
 *
 * <AUTHOR> Assistant
 */
public class HierarchicalSorter extends Sorter {
    
    private static final Logger logger = LoggerFactory.getLogger(HierarchicalSorter.class);
    
    private final HierarchicalSortConfig config;
    private final HierarchicalFeature hierarchicalFeature;
    private final HierarchicalSortMonitor monitor;
    private final Sorter fallbackSorter;
    
    public HierarchicalSorter(HierarchicalSortConfig config,
                             List<Feature> traditionalFeatures,
                             Sorter fallbackSorter) {
        // 调用父类构造函数，创建一个简单的权重评分器
        super(createDefaultScorer());

        Assert.notNull(config, "HierarchicalSortConfig cannot be null");
        Assert.notNull(traditionalFeatures, "Traditional features cannot be null");

        this.config = config;
        this.hierarchicalFeature = new HierarchicalFeature(config, traditionalFeatures);
        this.monitor = new HierarchicalSortMonitor(config);
        this.fallbackSorter = fallbackSorter;

        // 添加分层排序特征项
        this.addFeature(hierarchicalFeature);

        // 验证配置
        if (!config.isValid()) {
            throw new IllegalArgumentException("Invalid hierarchical sort config");
        }
    }

    /**
     * 创建默认的评分器
     */
    private static Scorer createDefaultScorer() {
        // 为分层排序创建一个简单的权重评分器
        java.util.Map<String, Double> weights = new java.util.HashMap<>();
        weights.put("HIERARCHICAL_SORT", 1.0);
        return new WeightScorer(weights);
    }
    
    /**
     * 执行分层排序
     * @param models 待排序的司机模型列表
     * @param context 排序上下文
     * @return 排序后的司机模型列表
     */
    @Override
    public List<SortModel> sort(List<SortModel> models, SortContext context) {
        if (CollectionUtils.isEmpty(models)) {
            return Collections.emptyList();
        }
        
        Stopwatch stopwatch = Stopwatch.createStarted();
        boolean usedFallback = false;
        
        try {
            logger.info("HierarchicalSort_Start", 
                    String.format("开始分层排序，司机数量: %d, 配置: %s", models.size(), config.getConfigId()));
            
            // 检查是否启用分层排序
            if (!config.isEnabled()) {
                logger.info("HierarchicalSort_Disabled", "分层排序未启用，使用传统排序");
                return fallbackToTraditionalSort(models, context);
            }

            // 使用父类的排序逻辑，会自动调用特征项计算和评分
            models = super.sort(models, context);

            // 记录排序结果
            recordSortingResults(models, context, stopwatch.elapsed(TimeUnit.MILLISECONDS));
            
            logger.info("HierarchicalSort_Success", 
                    String.format("分层排序完成，耗时: %d ms", stopwatch.elapsed(TimeUnit.MILLISECONDS)));
            
        } catch (Exception e) {
            logger.error("HierarchicalSort_Error", "分层排序执行失败，降级到传统排序", e);
            usedFallback = true;
            
            // 降级到传统排序
            models = fallbackToTraditionalSort(models, context);
            
            // 记录降级事件
            monitor.recordFallbackEvent(e.getMessage(), context);
        }
        
        // 记录监控指标
        monitor.recordSortingMetrics(models.size(), stopwatch.elapsed(TimeUnit.MILLISECONDS), usedFallback);
        
        return models;
    }
    
    /**
     * 获取分层排序分数
     */
    private Double getHierarchicalScore(SortModel model) {
        return model.getValues().stream()
                .filter(value -> "HIERARCHICAL_SORT".equals(value.getName()))
                .findFirst()
                .map(value -> value.getValue())
                .orElse(0.0);
    }
    
    /**
     * 降级到传统排序
     */
    private List<SortModel> fallbackToTraditionalSort(List<SortModel> models, SortContext context) {
        if (fallbackSorter != null) {
            logger.info("HierarchicalSort_Fallback", "使用传统排序器进行降级排序");
            return fallbackSorter.sort(models, context);
        } else {
            logger.warn("HierarchicalSort_NoFallback", "没有配置降级排序器，使用默认排序");
            // 简单的默认排序：按司机ID排序
            models.sort(Comparator.comparing(model -> model.getModel().getDriver().getDriverId()));
            return models;
        }
    }
    
    /**
     * 记录排序结果
     */
    private void recordSortingResults(List<SortModel> models, SortContext context, long elapsedTime) {
        try {
            if (!config.isMonitoringEnabled()) {
                return;
            }
            
            // 记录排序结果统计
            int totalDrivers = models.size();
            long successfulCalculations = models.stream()
                    .mapToLong(model -> {
                        return model.getValues().stream()
                                .filter(value -> "HIERARCHICAL_SORT".equals(value.getName()))
                                .filter(value -> {
                                    Object fallback = value.getValueDetailMap().get("fallback");
                                    return fallback == null || !Boolean.TRUE.equals(fallback);
                                })
                                .count();
                    })
                    .sum();
            
            double successRate = totalDrivers > 0 ? (double) successfulCalculations / totalDrivers : 0.0;
            
            logger.info("HierarchicalSort_Results", 
                    String.format("排序结果统计 - 总司机数: %d, 成功计算: %d, 成功率: %.2f%%, 耗时: %d ms", 
                            totalDrivers, successfulCalculations, successRate * 100, elapsedTime));
            
            // 记录前10名司机的分数分布
            models.stream()
                    .limit(10)
                    .forEach(model -> {
                        Long driverId = model.getModel().getDriver().getDriverId();
                        Double score = getHierarchicalScore(model);
                        logger.info("HierarchicalSort_TopDrivers", 
                                String.format("司机ID: %d, 分层排序分数: %.2f", driverId, score));
                    });
                    
        } catch (Exception e) {
            logger.warn("记录排序结果失败", e);
        }
    }
    
    /**
     * 获取配置信息
     */
    public HierarchicalSortConfig getConfig() {
        return config;
    }
    
    /**
     * 获取监控器
     */
    public HierarchicalSortMonitor getMonitor() {
        return monitor;
    }
    
    /**
     * 获取分层特征项
     */
    public HierarchicalFeature getHierarchicalFeature() {
        return hierarchicalFeature;
    }
    
    /**
     * 检查排序器健康状态
     */
    public boolean isHealthy() {
        try {
            return config.isEnabled() && config.isValid() && monitor.isHealthy();
        } catch (Exception e) {
            logger.warn("健康检查失败", e);
            return false;
        }
    }
    
    /**
     * 获取排序器状态信息
     */
    public String getStatusInfo() {
        return String.format("HierarchicalSorter[config=%s, enabled=%s, healthy=%s]", 
                config.getConfigId(), config.isEnabled(), isHealthy());
    }
}
