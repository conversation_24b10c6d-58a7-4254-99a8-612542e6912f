package com.ctrip.dcs.domain.schedule.sort.hierarchical.category;

import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.feature.Feature;
import com.ctrip.dcs.domain.schedule.sort.feature.Value;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.FeatureConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.HierarchicalSortConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.grade.HierarchicalScore;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.grade.HierarchicalScoreCalculator;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分层排序特征项
 * 整合所有传统特征项，按照四大类进行分层计算
 * 
 * <AUTHOR> Assistant
 */
public class HierarchicalFeature implements Feature {
    
    private static final Logger logger = LoggerFactory.getLogger(HierarchicalFeature.class);
    
    private final HierarchicalSortConfig config;
    private final List<Feature> traditionalFeatures;
    private final HierarchicalScoreCalculator calculator;
    
    public HierarchicalFeature(HierarchicalSortConfig config, List<Feature> traditionalFeatures) {
        this.config = config;
        this.traditionalFeatures = traditionalFeatures;
        this.calculator = new HierarchicalScoreCalculator(config);
    }
    
    @Override
    public List<Value> value(List<SortModel> models, SortContext context) {
        List<Value> hierarchicalValues = new ArrayList<>();
        
        try {
            // 如果分层排序未启用，降级到传统排序
            if (!config.isEnabled()) {
                return fallbackToTraditionalSorting(models, context);
            }
            
            // 首先执行所有传统特征项的计算
            executeTraditionalFeatures(models, context);
            
            // 计算分层排序分数
            List<HierarchicalScore> hierarchicalScores = calculator.calculateScores(models, context);
            
            // 检查计算成功率，如果失败率过高则降级
            double successRate = calculateSuccessRate(hierarchicalScores);
            if (successRate < (1.0 - config.getFallbackThreshold())) {
                logger.warn("分层排序成功率过低，降级到传统排序", "successRate: " + successRate);
                return fallbackToTraditionalSorting(models, context);
            }
            
            // 为每个模型创建分层排序值
            for (int i = 0; i < models.size(); i++) {
                SortModel model = models.get(i);
                HierarchicalScore score = hierarchicalScores.get(i);
                
                if (score.isSuccessful()) {
                    // 创建分层排序值
                    Value hierarchicalValue = createHierarchicalValue(score);
                    hierarchicalValues.add(hierarchicalValue);
                    model.addValue(hierarchicalValue);
                    
                    // 如果启用监控，记录详细信息
                    if (config.isMonitoringEnabled()) {
                        recordMonitoringData(score, context);
                    }
                } else {
                    // 计算失败，使用降级分数
                    Value fallbackValue = createFallbackValue(model, context);
                    hierarchicalValues.add(fallbackValue);
                    model.addValue(fallbackValue);
                }
            }
            
        } catch (Exception e) {
            logger.error("分层排序计算异常，降级到传统排序", e);
            return fallbackToTraditionalSorting(models, context);
        }
        
        return hierarchicalValues;
    }
    
    /**
     * 执行传统特征项计算
     */
    private void executeTraditionalFeatures(List<SortModel> models, SortContext context) {
        for (Feature feature : traditionalFeatures) {
            try {
                feature.value(models, context);
            } catch (Exception e) {
                logger.warn("传统特征项计算失败", "feature: " + feature.getClass().getSimpleName(), e);
            }
        }
    }
    
    /**
     * 创建分层排序值
     */
    private Value createHierarchicalValue(HierarchicalScore score) {
        // 使用最终分数作为排序值
        double sortValue = score.getFinalScore();
        
        // 创建详细信息映射
        Map<String, Object> details = Map.of(
                "overallGrade", score.getOverallGrade().getCode(),
                "overallGpa", score.getOverallGpa(),
                "fineGrainedScore", score.getFineGrainedScore(),
                "aggregationStrategy", score.getAggregationStrategy().getCode(),
                "categoryCount", score.getTotalCategoryCount(),
                "successRate", score.getSuccessRate(),
                "computationTime", score.getComputationTime()
        );
        
        return new Value("HIERARCHICAL_SORT", sortValue, details);
    }
    
    /**
     * 创建降级值
     */
    private Value createFallbackValue(SortModel model, SortContext context) {
        // 使用传统排序的加权和作为降级分数
        double fallbackScore = calculateTraditionalWeightedScore(model);
        
        Map<String, Object> details = Map.of(
                "fallback", true,
                "reason", "hierarchical_calculation_failed"
        );
        
        return new Value("HIERARCHICAL_SORT", fallbackScore, details);
    }
    
    /**
     * 计算传统排序的加权分数
     */
    private double calculateTraditionalWeightedScore(SortModel model) {
        double totalScore = 0.0;
        double totalWeight = 0.0;
        
        // 获取传统特征项的权重配置
        Map<String, Double> traditionalWeights = getTraditionalFeatureWeights();
        
        for (Value value : model.getValues()) {
            String featureName = value.getName();
            if (traditionalWeights.containsKey(featureName)) {
                double weight = traditionalWeights.get(featureName);
                totalScore += value.getValue() * weight;
                totalWeight += weight;
            }
        }
        
        return totalWeight > 0 ? totalScore / totalWeight : 0.0;
    }
    
    /**
     * 获取传统特征项权重配置
     */
    private Map<String, Double> getTraditionalFeatureWeights() {
        // 基于现有的201排序项权重
        return Map.of(
                "F2", 1.0,    // 司机分占比
                "F9", 0.8,    // 局部时间间隔
                "F10", 0.8,   // 局部空驶距离
                "F11", 0.4,   // 未来接单能力
                "F13", 0.75,  // 司机日收益
                "F14", 1.0,   // 订单里程价值
                "F19", 1.0    // 司机分层
        );
    }
    
    /**
     * 降级到传统排序
     */
    private List<Value> fallbackToTraditionalSorting(List<SortModel> models, SortContext context) {
        List<Value> fallbackValues = new ArrayList<>();
        
        // 确保传统特征项已计算
        executeTraditionalFeatures(models, context);
        
        for (SortModel model : models) {
            double fallbackScore = calculateTraditionalWeightedScore(model);
            Value fallbackValue = new Value("HIERARCHICAL_SORT", fallbackScore, 
                    Map.of("fallback", true, "reason", "system_fallback"));
            
            fallbackValues.add(fallbackValue);
            model.addValue(fallbackValue);
        }
        
        return fallbackValues;
    }
    
    /**
     * 计算成功率
     */
    private double calculateSuccessRate(List<HierarchicalScore> scores) {
        if (scores.isEmpty()) {
            return 0.0;
        }
        
        long successCount = scores.stream().mapToLong(score -> score.isSuccessful() ? 1 : 0).sum();
        return (double) successCount / scores.size();
    }
    
    /**
     * 记录监控数据
     */
    private void recordMonitoringData(HierarchicalScore score, SortContext context) {
        try {
            // 记录各大类分数分布
            for (SortCategory category : SortCategory.values()) {
                double categoryScore = score.getCategoryRawScore(category);
                String gradeCode = score.getCategoryGrade(category).getCode();
                
                // 这里可以集成到监控系统
                logger.info("HierarchicalSortMonitoring", 
                        String.format("driverId=%d, category=%s, score=%.2f, grade=%s", 
                                score.getDriverId(), category.getCode(), categoryScore, gradeCode));
            }
            
            // 记录总体分数
            logger.info("HierarchicalSortOverall", 
                    String.format("driverId=%d, overallGrade=%s, gpa=%.2f, finalScore=%.2f, computationTime=%d", 
                            score.getDriverId(), score.getOverallGrade().getCode(), 
                            score.getOverallGpa(), score.getFinalScore(), score.getComputationTime()));
                            
        } catch (Exception e) {
            logger.warn("监控数据记录失败", e);
        }
    }
    
    /**
     * 获取特征项配置
     */
    public HierarchicalSortConfig getConfig() {
        return config;
    }
    
    /**
     * 获取传统特征项列表
     */
    public List<Feature> getTraditionalFeatures() {
        return traditionalFeatures;
    }
}
