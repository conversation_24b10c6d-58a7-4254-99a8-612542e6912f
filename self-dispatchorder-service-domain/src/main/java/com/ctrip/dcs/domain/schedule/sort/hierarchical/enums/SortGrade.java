package com.ctrip.dcs.domain.schedule.sort.hierarchical.enums;

/**
 * 排序等级枚举
 * 定义司机在各个大类中的等级评定，用于分层排序
 * 
 * <AUTHOR> Assistant
 */
public enum SortGrade {
    
    /**
     * A级 - 优秀
     * 分数范围：[85, 100]
     * GPA值：4.0
     */
    A("A", "优秀", 4.0, 85.0, 100.0),
    
    /**
     * B级 - 良好
     * 分数范围：[70, 85)
     * GPA值：3.0
     */
    B("B", "良好", 3.0, 70.0, 85.0),
    
    /**
     * C级 - 一般
     * 分数范围：[50, 70)
     * GPA值：2.0
     */
    C("C", "一般", 2.0, 50.0, 70.0),
    
    /**
     * D级 - 较差
     * 分数范围：[0, 50)
     * GPA值：1.0
     */
    D("D", "较差", 1.0, 0.0, 50.0);
    
    private final String code;
    private final String description;
    private final double gpaValue;
    private final double minScore;
    private final double maxScore;
    
    SortGrade(String code, String description, double gpaValue, double minScore, double maxScore) {
        this.code = code;
        this.description = description;
        this.gpaValue = gpaValue;
        this.minScore = minScore;
        this.maxScore = maxScore;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public double getGpaValue() {
        return gpaValue;
    }
    
    public double getMinScore() {
        return minScore;
    }
    
    public double getMaxScore() {
        return maxScore;
    }
    
    /**
     * 根据分数获取对应的等级
     * @param score 分数 [0, 100]
     * @return 对应的等级
     */
    public static SortGrade fromScore(double score) {
        for (SortGrade grade : values()) {
            if (score >= grade.getMinScore() && score < grade.getMaxScore()) {
                return grade;
            }
        }
        // 处理边界情况，100分归为A级
        if (score >= 100.0) {
            return A;
        }
        // 负分或其他异常情况归为D级
        return D;
    }
    
    /**
     * 根据代码获取等级
     * @param code 等级代码
     * @return 等级枚举
     */
    public static SortGrade fromCode(String code) {
        for (SortGrade grade : values()) {
            if (grade.getCode().equals(code)) {
                return grade;
            }
        }
        throw new IllegalArgumentException("Unknown sort grade code: " + code);
    }
    
    /**
     * 判断是否为优秀等级（A级）
     * @return true if A级
     */
    public boolean isExcellent() {
        return this == A;
    }
    
    /**
     * 判断是否为较差等级（D级）
     * @return true if D级
     */
    public boolean isPoor() {
        return this == D;
    }
}
