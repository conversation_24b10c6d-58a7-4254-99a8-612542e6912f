package com.ctrip.dcs.domain.schedule.sort.hierarchical.config;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.NormalizationType;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import lombok.Builder;
import lombok.Getter;

/**
 * 特征项配置类
 * 定义单个特征项的配置信息，包括归属大类、权重、归一化方式等
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Builder
public class FeatureConfig {
    
    /**
     * 特征项ID，对应FeatureItemId中的枚举值
     */
    private String featureId;
    
    /**
     * 特征项名称
     */
    private String featureName;
    
    /**
     * 特征项描述
     */
    private String description;
    
    /**
     * 归属的排序大类
     */
    private SortCategory category;
    
    /**
     * 在所属大类中的权重
     * 同一大类下所有特征项的权重之和应该等于1.0
     */
    private double weight;
    
    /**
     * 归一化方式
     */
    @Builder.Default
    private NormalizationType normalizationType = NormalizationType.MIN_MAX;
    
    /**
     * 是否启用该特征项
     */
    @Builder.Default
    private boolean enabled = true;
    
    /**
     * 是否为关键特征项
     * 关键特征项计算失败时会影响整个大类的计算
     */
    @Builder.Default
    private boolean critical = false;
    
    /**
     * 自定义归一化参数
     * 用于存储特定归一化方式需要的参数
     * 例如：对数归一化的底数、Sigmoid的参数等
     */
    private NormalizationParams normalizationParams;
    
    /**
     * 城市特定权重
     * key: 城市ID
     * value: 该城市下的权重
     */
    private java.util.Map<Integer, Double> citySpecificWeights;
    
    /**
     * 时段特定权重
     * key: 时段标识
     * value: 该时段下的权重
     */
    private java.util.Map<String, Double> timeSpecificWeights;
    
    /**
     * 获取指定城市的权重
     * @param cityId 城市ID
     * @return 权重值，如果没有城市特定配置则返回默认权重
     */
    public double getWeight(Integer cityId) {
        if (citySpecificWeights != null && citySpecificWeights.containsKey(cityId)) {
            return citySpecificWeights.get(cityId);
        }
        return weight;
    }
    
    /**
     * 获取指定时段的权重
     * @param timeSlot 时段标识
     * @return 权重值，如果没有时段特定配置则返回默认权重
     */
    public double getWeight(String timeSlot) {
        if (timeSpecificWeights != null && timeSpecificWeights.containsKey(timeSlot)) {
            return timeSpecificWeights.get(timeSlot);
        }
        return weight;
    }
    
    /**
     * 获取指定城市和时段的权重
     * @param cityId 城市ID
     * @param timeSlot 时段标识
     * @return 权重值，优先级：时段特定 > 城市特定 > 默认权重
     */
    public double getWeight(Integer cityId, String timeSlot) {
        // 优先使用时段特定权重
        if (timeSpecificWeights != null && timeSpecificWeights.containsKey(timeSlot)) {
            return timeSpecificWeights.get(timeSlot);
        }
        // 其次使用城市特定权重
        if (citySpecificWeights != null && citySpecificWeights.containsKey(cityId)) {
            return citySpecificWeights.get(cityId);
        }
        // 最后使用默认权重
        return weight;
    }
    
    /**
     * 归一化参数配置类
     */
    @Getter
    @Builder
    public static class NormalizationParams {
        
        /**
         * 对数归一化的底数
         */
        @Builder.Default
        private double logBase = Math.E;
        
        /**
         * Sigmoid函数的斜率参数
         */
        @Builder.Default
        private double sigmoidSlope = 1.0;
        
        /**
         * Sigmoid函数的中心点
         */
        @Builder.Default
        private double sigmoidCenter = 0.0;
        
        /**
         * 自定义最小值（用于Min-Max归一化）
         */
        private Double customMin;
        
        /**
         * 自定义最大值（用于Min-Max归一化）
         */
        private Double customMax;
        
        /**
         * Z-Score归一化的自定义均值
         */
        private Double customMean;
        
        /**
         * Z-Score归一化的自定义标准差
         */
        private Double customStdDev;
        
        /**
         * 是否启用异常值处理
         * 在归一化前移除或调整异常值
         */
        @Builder.Default
        private boolean outlierHandling = false;
        
        /**
         * 异常值阈值（标准差倍数）
         */
        @Builder.Default
        private double outlierThreshold = 3.0;
    }
}
