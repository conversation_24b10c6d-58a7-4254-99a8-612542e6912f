package com.ctrip.dcs.domain.schedule.sort.hierarchical.enums;

/**
 * 归一化方式枚举
 * 定义各种归一化算法类型，用于将不同量纲的特征值标准化到统一范围
 * 
 * <AUTHOR> Assistant
 */
public enum NormalizationType {
    
    /**
     * Min-Max归一化（线性缩放）
     * 公式：x' = (x - min) / (max - min)
     * 结果范围：[0, 1]
     * 适用：数据分布相对均匀的情况
     */
    MIN_MAX("MIN_MAX", "Min-Max归一化", "线性缩放到[0,1]区间"),
    
    /**
     * Z-Score标准化（标准差归一化）
     * 公式：x' = (x - μ) / σ
     * 结果范围：(-∞, +∞)，但大部分值在[-3, 3]
     * 适用：数据符合正态分布的情况
     */
    Z_SCORE("Z_SCORE", "Z-Score标准化", "标准差归一化，均值0标准差1"),
    
    /**
     * 对数归一化
     * 公式：x' = log(x) / log(max)
     * 结果范围：[0, 1]
     * 适用：长尾分布数据，如收入、距离等
     */
    LOG_NORMALIZATION("LOG_NORMALIZATION", "对数归一化", "适用于长尾分布数据"),
    
    /**
     * Sigmoid映射
     * 公式：x' = 1 / (1 + e^(-x))
     * 结果范围：(0, 1)
     * 适用：需要平滑映射的情况
     */
    SIGMOID("SIGMOID", "Sigmoid映射", "S型曲线映射到(0,1)"),
    
    /**
     * 排名归一化（分位数归一化）
     * 公式：x' = rank(x) / N
     * 结果范围：[0, 1]
     * 适用：关注相对排名而非绝对值的情况
     */
    RANK_NORMALIZATION("RANK_NORMALIZATION", "排名归一化", "基于排名的分位数归一化"),
    
    /**
     * 逆向Min-Max归一化
     * 公式：x' = (max - x) / (max - min)
     * 结果范围：[0, 1]
     * 适用：值越小越好的指标（如空驶距离、等待时间）
     */
    INVERSE_MIN_MAX("INVERSE_MIN_MAX", "逆向Min-Max归一化", "值越小分数越高"),
    
    /**
     * 无归一化
     * 直接使用原始值，不进行任何变换
     * 适用：已经是标准化分数的情况
     */
    NONE("NONE", "无归一化", "直接使用原始值");
    
    private final String code;
    private final String name;
    private final String description;
    
    NormalizationType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取归一化类型
     * @param code 归一化类型代码
     * @return 归一化类型枚举
     */
    public static NormalizationType fromCode(String code) {
        for (NormalizationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown normalization type code: " + code);
    }
    
    /**
     * 判断是否需要统计信息（最大值、最小值、均值、标准差等）
     * @return true if 需要统计信息
     */
    public boolean requiresStatistics() {
        return this == MIN_MAX || this == Z_SCORE || this == LOG_NORMALIZATION || 
               this == RANK_NORMALIZATION || this == INVERSE_MIN_MAX;
    }
    
    /**
     * 判断是否为逆向归一化（值越小分数越高）
     * @return true if 逆向归一化
     */
    public boolean isInverse() {
        return this == INVERSE_MIN_MAX;
    }
}
