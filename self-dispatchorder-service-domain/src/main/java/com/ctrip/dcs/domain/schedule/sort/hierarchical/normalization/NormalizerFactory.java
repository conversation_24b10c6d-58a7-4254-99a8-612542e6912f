package com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.NormalizationType;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.impl.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 归一化器工厂类
 * 负责创建和管理各种类型的归一化器
 * 
 * <AUTHOR> Assistant
 */
public class NormalizerFactory {
    
    private static final Map<NormalizationType, HierarchicalNormalizer> NORMALIZER_CACHE = new HashMap<>();
    
    static {
        // 初始化所有归一化器
        NORMALIZER_CACHE.put(NormalizationType.MIN_MAX, new MinMaxNormalizer());
        NORMALIZER_CACHE.put(NormalizationType.INVERSE_MIN_MAX, new InverseMinMaxNormalizer());
        NORMALIZER_CACHE.put(NormalizationType.Z_SCORE, new ZScoreNormalizer());
        NORMALIZER_CACHE.put(NormalizationType.LOG_NORMALIZATION, new LogNormalizer());
        NORMALIZER_CACHE.put(NormalizationType.SIGMOID, new SigmoidNormalizer());
        NORMALIZER_CACHE.put(NormalizationType.RANK_NORMALIZATION, new RankNormalizer());
        NORMALIZER_CACHE.put(NormalizationType.NONE, new NoNormalizer());
    }
    
    /**
     * 获取指定类型的归一化器
     * @param type 归一化类型
     * @return 归一化器实例
     * @throws IllegalArgumentException 如果类型不支持
     */
    public static HierarchicalNormalizer getNormalizer(NormalizationType type) {
        HierarchicalNormalizer normalizer = NORMALIZER_CACHE.get(type);
        if (normalizer == null) {
            throw new IllegalArgumentException("Unsupported normalization type: " + type);
        }
        return normalizer;
    }
    
    /**
     * 检查是否支持指定的归一化类型
     * @param type 归一化类型
     * @return true if 支持
     */
    public static boolean isSupported(NormalizationType type) {
        return NORMALIZER_CACHE.containsKey(type);
    }
    
    /**
     * 获取所有支持的归一化类型
     * @return 支持的归一化类型数组
     */
    public static NormalizationType[] getSupportedTypes() {
        return NORMALIZER_CACHE.keySet().toArray(new NormalizationType[0]);
    }
    
    /**
     * 注册自定义归一化器
     * @param type 归一化类型
     * @param normalizer 归一化器实例
     */
    public static void registerNormalizer(NormalizationType type, HierarchicalNormalizer normalizer) {
        if (type == null || normalizer == null) {
            throw new IllegalArgumentException("Type and normalizer cannot be null");
        }
        NORMALIZER_CACHE.put(type, normalizer);
    }
    
    /**
     * 创建自适应归一化器
     * 根据数据特征自动选择最适合的归一化方式
     * @param statistics 数据统计信息
     * @return 推荐的归一化类型
     */
    public static NormalizationType recommendNormalizationType(NormalizationStatistics statistics) {
        if (statistics == null || !statistics.isValid()) {
            return NormalizationType.NONE;
        }
        
        // 如果数据是常数，不需要归一化
        if (statistics.isConstant()) {
            return NormalizationType.NONE;
        }
        
        // 如果异常值比例较高，使用排名归一化
        if (statistics.getOutlierRatio() > 0.2) {
            return NormalizationType.RANK_NORMALIZATION;
        }
        
        // 如果数据范围很大且偏斜，使用对数归一化
        double skewness = calculateSkewness(statistics);
        if (statistics.getRange() > 1000 && Math.abs(skewness) > 1.5) {
            return NormalizationType.LOG_NORMALIZATION;
        }
        
        // 如果数据接近正态分布，使用Z-Score
        if (Math.abs(skewness) < 0.5) {
            return NormalizationType.Z_SCORE;
        }
        
        // 默认使用Min-Max归一化
        return NormalizationType.MIN_MAX;
    }
    
    /**
     * 计算偏度（简化版本）
     * @param statistics 统计信息
     * @return 偏度值
     */
    private static double calculateSkewness(NormalizationStatistics statistics) {
        // 简化的偏度计算：(mean - median) / stdDev
        if (statistics.getStdDev() == 0) {
            return 0.0;
        }
        return (statistics.getMean() - statistics.getMedian()) / statistics.getStdDev();
    }
}
