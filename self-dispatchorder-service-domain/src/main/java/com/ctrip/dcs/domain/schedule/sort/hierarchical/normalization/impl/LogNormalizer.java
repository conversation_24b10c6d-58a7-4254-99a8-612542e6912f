package com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.impl;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.FeatureConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.NormalizationType;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.HierarchicalNormalizer;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.NormalizationStatistics;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 对数归一化器
 * 适用于长尾分布数据，如收入、距离等
 * 公式：x' = log(x + 1) / log(max + 1)
 * 
 * <AUTHOR> Assistant
 */
public class LogNormalizer implements HierarchicalNormalizer {
    
    @Override
    public List<Double> normalize(List<Double> values, FeatureConfig config) {
        if (values == null || values.isEmpty()) {
            return values;
        }
        
        // 计算统计信息
        NormalizationStatistics statistics = NormalizationStatistics.fromValues(values);
        
        // 处理异常值（如果启用）
        List<Double> processedValues = values;
        if (config.getNormalizationParams() != null && 
            config.getNormalizationParams().isOutlierHandling()) {
            processedValues = handleOutliers(values, statistics, config);
            // 重新计算统计信息
            statistics = NormalizationStatistics.fromValues(processedValues);
        }
        
        // 执行归一化
        return processedValues.stream()
                .map(value -> normalize(value, statistics, config))
                .collect(Collectors.toList());
    }
    
    @Override
    public double normalize(double value, NormalizationStatistics statistics, FeatureConfig config) {
        // 获取对数底数
        double logBase = getLogBase(config);
        
        // 处理负值：加上偏移量使所有值为正
        double offset = Math.max(0, -statistics.getMin() + 1);
        double adjustedValue = value + offset;
        double adjustedMax = statistics.getMax() + offset;
        
        // 处理零值和负值
        if (adjustedValue <= 0) {
            return 0.0;
        }
        
        // 对数归一化
        double logValue = Math.log(adjustedValue) / Math.log(logBase);
        double logMax = Math.log(adjustedMax) / Math.log(logBase);
        
        // 处理常数情况
        if (Math.abs(logMax) < 1e-10) {
            return 0.5;
        }
        
        double normalized = logValue / logMax;
        
        // 确保结果在[0,1]范围内
        return Math.max(0.0, Math.min(1.0, normalized));
    }
    
    @Override
    public NormalizationType getSupportedType() {
        return NormalizationType.LOG_NORMALIZATION;
    }
    
    @Override
    public boolean requiresStatistics() {
        return true;
    }
    
    @Override
    public List<Double> handleOutliers(List<Double> values, NormalizationStatistics statistics, FeatureConfig config) {
        if (config.getNormalizationParams() == null || !config.getNormalizationParams().isOutlierHandling()) {
            return values;
        }
        
        // 对于对数归一化，异常值处理需要在对数空间进行
        double threshold = config.getNormalizationParams().getOutlierThreshold();
        
        // 先进行对数变换
        List<Double> logValues = values.stream()
                .map(v -> Math.log(Math.max(v, 1e-10))) // 避免log(0)
                .collect(Collectors.toList());
        
        // 计算对数空间的统计信息
        NormalizationStatistics logStats = NormalizationStatistics.fromValues(logValues);
        double logMean = logStats.getMean();
        double logStdDev = logStats.getStdDev();
        
        // 在对数空间处理异常值，然后转换回原空间
        return values.stream()
                .map(value -> {
                    double logValue = Math.log(Math.max(value, 1e-10));
                    if (Math.abs(logValue - logMean) > threshold * logStdDev) {
                        // 调整异常值
                        double adjustedLogValue;
                        if (logValue > logMean) {
                            adjustedLogValue = logMean + threshold * logStdDev;
                        } else {
                            adjustedLogValue = logMean - threshold * logStdDev;
                        }
                        return Math.exp(adjustedLogValue);
                    }
                    return value;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 获取对数底数
     */
    private double getLogBase(FeatureConfig config) {
        if (config.getNormalizationParams() != null) {
            return config.getNormalizationParams().getLogBase();
        }
        return Math.E; // 默认使用自然对数
    }
    
    @Override
    public String getOutputRange() {
        return "[0, 1] (对数缩放，适用于长尾分布)";
    }
}
