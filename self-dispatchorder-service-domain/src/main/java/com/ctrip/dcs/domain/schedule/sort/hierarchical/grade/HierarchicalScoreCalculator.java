package com.ctrip.dcs.domain.schedule.sort.hierarchical.grade;

import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.feature.Value;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.FeatureConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.HierarchicalSortConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.AggregationStrategy;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortGrade;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.HierarchicalNormalizer;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.NormalizationStatistics;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.NormalizerFactory;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 分层排序分数计算器
 * 负责计算司机的分层排序分数
 * 
 * <AUTHOR> Assistant
 */
public class HierarchicalScoreCalculator {
    
    private static final Logger logger = LoggerFactory.getLogger(HierarchicalScoreCalculator.class);
    
    private final HierarchicalSortConfig config;
    
    public HierarchicalScoreCalculator(HierarchicalSortConfig config) {
        this.config = config;
    }
    
    /**
     * 计算单个司机的分层排序分数
     * @param model 排序模型
     * @param context 排序上下文
     * @return 分层排序分数
     */
    public HierarchicalScore calculateScore(SortModel model, SortContext context) {
        long startTime = System.currentTimeMillis();
        List<String> errors = new ArrayList<>();
        
        try {
            // 获取司机ID
            Long driverId = model.getModel().getDriver().getDriverId();
            
            // 获取适用的权重配置
            Map<SortCategory, Double> categoryWeights = getCategoryWeights(context);
            
            // 计算各大类分数
            Map<SortCategory, CategoryScore> categoryScores = new HashMap<>();
            for (SortCategory category : SortCategory.values()) {
                try {
                    CategoryScore categoryScore = calculateCategoryScore(
                            category, model, context, categoryWeights.get(category));
                    categoryScores.put(category, categoryScore);
                } catch (Exception e) {
                    logger.warn("计算大类分数失败", "category: " + category, e);
                    errors.add("大类 " + category.getDescription() + " 计算失败: " + e.getMessage());
                    categoryScores.put(category, CategoryScore.createFailed(category, e.getMessage()));
                }
            }
            
            // 计算总体等级和GPA
            AggregationStrategy strategy = config.getAggregationStrategy();
            SortGrade overallGrade = calculateOverallGrade(categoryScores, strategy);
            double overallGpa = calculateOverallGpa(categoryScores, categoryWeights, strategy);
            
            // 计算最终分数
            double finalScore = calculateFinalScore(overallGrade, overallGpa, categoryScores);
            
            // 计算细分分数（如果启用）
            double fineGrainedScore = 0.0;
            if (config.isEnableFineGrainedScoring()) {
                fineGrainedScore = calculateFineGrainedScore(categoryScores, categoryWeights);
            }
            
            long computationTime = System.currentTimeMillis() - startTime;
            
            return HierarchicalScore.builder()
                    .driverId(driverId)
                    .categoryScores(categoryScores)
                    .overallGrade(overallGrade)
                    .overallGpa(overallGpa)
                    .finalScore(finalScore)
                    .aggregationStrategy(strategy)
                    .fineGrainedScoring(config.isEnableFineGrainedScoring())
                    .fineGrainedScore(fineGrainedScore)
                    .successful(errors.isEmpty())
                    .errorMessages(errors)
                    .computationTime(computationTime)
                    .build();
                    
        } catch (Exception e) {
            logger.error("分层排序计算失败", "driverId: " + model.getModel().getDriver().getDriverId(), e);
            errors.add("分层排序计算失败: " + e.getMessage());
            return HierarchicalScore.createFailed(model.getModel().getDriver().getDriverId(), errors);
        }
    }
    
    /**
     * 批量计算司机的分层排序分数
     * @param models 排序模型列表
     * @param context 排序上下文
     * @return 分层排序分数列表
     */
    public List<HierarchicalScore> calculateScores(List<SortModel> models, SortContext context) {
        return models.stream()
                .map(model -> calculateScore(model, context))
                .collect(Collectors.toList());
    }
    
    /**
     * 计算单个大类的分数
     */
    private CategoryScore calculateCategoryScore(SortCategory category, SortModel model, 
                                               SortContext context, Double weight) {
        // 获取该大类下的所有特征项配置
        Map<String, FeatureConfig> categoryFeatures = config.getFeatureConfigs().entrySet().stream()
                .filter(entry -> entry.getValue().getCategory() == category)
                .filter(entry -> entry.getValue().isEnabled())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        
        if (categoryFeatures.isEmpty()) {
            return CategoryScore.createDefault(category, weight != null ? weight : 0.0);
        }
        
        // 收集该大类的所有特征值
        Map<String, Double> featureScores = new HashMap<>();
        Map<String, Double> featureWeights = new HashMap<>();
        
        for (Map.Entry<String, FeatureConfig> entry : categoryFeatures.entrySet()) {
            String featureId = entry.getKey();
            FeatureConfig featureConfig = entry.getValue();
            
            // 从模型中获取特征值
            Optional<Value> valueOpt = model.getValues().stream()
                    .filter(v -> v.getName().equals(featureId))
                    .findFirst();
            
            if (valueOpt.isPresent()) {
                double rawValue = valueOpt.get().getValue();
                
                // 归一化处理
                double normalizedValue = normalizeFeatureValue(rawValue, featureConfig, context);
                
                // 转换为0-100分数
                double score = normalizedValue * 100.0;
                
                featureScores.put(featureId, score);
                featureWeights.put(featureId, featureConfig.getWeight(
                        context.getDspOrder().getCityId(), getCurrentTimeSlot(context)));
            }
        }
        
        // 计算大类加权平均分数
        double rawScore = calculateWeightedAverage(featureScores, featureWeights);
        
        // 映射到等级
        SortGrade grade = SortGrade.fromScore(rawScore);
        
        return CategoryScore.builder()
                .category(category)
                .rawScore(rawScore)
                .grade(grade)
                .weight(weight != null ? weight : 0.0)
                .weightedScore(rawScore * (weight != null ? weight : 0.0))
                .gpaValue(grade.getGpaValue())
                .weightedGpa(grade.getGpaValue() * (weight != null ? weight : 0.0))
                .featureScores(featureScores)
                .featureWeights(featureWeights)
                .successful(true)
                .build();
    }
    
    /**
     * 归一化特征值
     */
    private double normalizeFeatureValue(double rawValue, FeatureConfig featureConfig, SortContext context) {
        try {
            HierarchicalNormalizer normalizer = NormalizerFactory.getNormalizer(featureConfig.getNormalizationType());
            
            // 对于单个值的归一化，我们需要使用预设的统计信息或默认处理
            // 这里简化处理，实际应用中可能需要预计算统计信息
            if (normalizer.requiresStatistics()) {
                // 使用默认的归一化范围 [0, 1]
                return Math.max(0.0, Math.min(1.0, rawValue));
            } else {
                NormalizationStatistics dummyStats = NormalizationStatistics.builder().build();
                return normalizer.normalize(rawValue, dummyStats, featureConfig);
            }
        } catch (Exception e) {
            logger.warn("特征值归一化失败", "featureId: " + featureConfig.getFeatureId(), e);
            return rawValue; // 归一化失败时返回原值
        }
    }
    
    /**
     * 计算加权平均分数
     */
    private double calculateWeightedAverage(Map<String, Double> scores, Map<String, Double> weights) {
        if (scores.isEmpty()) {
            return 0.0;
        }
        
        double totalWeightedScore = 0.0;
        double totalWeight = 0.0;
        
        for (Map.Entry<String, Double> entry : scores.entrySet()) {
            String featureId = entry.getKey();
            double score = entry.getValue();
            double weight = weights.getOrDefault(featureId, 1.0);
            
            totalWeightedScore += score * weight;
            totalWeight += weight;
        }
        
        return totalWeight > 0 ? totalWeightedScore / totalWeight : 0.0;
    }
    
    /**
     * 计算总体等级
     */
    private SortGrade calculateOverallGrade(Map<SortCategory, CategoryScore> categoryScores, 
                                          AggregationStrategy strategy) {
        List<SortGrade> grades = categoryScores.values().stream()
                .filter(CategoryScore::isSuccessful)
                .map(CategoryScore::getGrade)
                .collect(Collectors.toList());
        
        if (grades.isEmpty()) {
            return SortGrade.D;
        }
        
        switch (strategy) {
            case MINIMUM_GRADE:
                return grades.stream().min(Comparator.comparing(SortGrade::getGpaValue)).orElse(SortGrade.D);
            case MAXIMUM_GRADE:
                return grades.stream().max(Comparator.comparing(SortGrade::getGpaValue)).orElse(SortGrade.D);
            case MAJORITY_VOTE:
                return calculateMajorityGrade(grades);
            case HYBRID_STRATEGY:
                // 如果有D级则直接返回D，否则按加权平均
                if (grades.contains(SortGrade.D)) {
                    return SortGrade.D;
                }
                // 继续执行加权平均逻辑
            case WEIGHTED_AVERAGE_GPA:
            default:
                double avgGpa = grades.stream().mapToDouble(SortGrade::getGpaValue).average().orElse(1.0);
                return SortGrade.fromScore(avgGpa * 25); // 将GPA转换为分数
        }
    }
    
    /**
     * 计算多数决等级
     */
    private SortGrade calculateMajorityGrade(List<SortGrade> grades) {
        Map<SortGrade, Long> gradeCount = grades.stream()
                .collect(Collectors.groupingBy(g -> g, Collectors.counting()));
        
        return gradeCount.entrySet().stream()
                .max(Map.Entry.<SortGrade, Long>comparingByValue()
                        .thenComparing(entry -> entry.getKey().getGpaValue()))
                .map(Map.Entry::getKey)
                .orElse(SortGrade.D);
    }
    
    /**
     * 计算总体GPA
     */
    private double calculateOverallGpa(Map<SortCategory, CategoryScore> categoryScores, 
                                     Map<SortCategory, Double> weights, AggregationStrategy strategy) {
        if (strategy == AggregationStrategy.WEIGHTED_AVERAGE_GPA || strategy == AggregationStrategy.HYBRID_STRATEGY) {
            double totalWeightedGpa = 0.0;
            double totalWeight = 0.0;
            
            for (Map.Entry<SortCategory, CategoryScore> entry : categoryScores.entrySet()) {
                if (entry.getValue().isSuccessful()) {
                    double weight = weights.getOrDefault(entry.getKey(), 0.0);
                    totalWeightedGpa += entry.getValue().getGpaValue() * weight;
                    totalWeight += weight;
                }
            }
            
            return totalWeight > 0 ? totalWeightedGpa / totalWeight : 1.0;
        } else {
            // 其他策略使用等级对应的GPA
            SortGrade overallGrade = calculateOverallGrade(categoryScores, strategy);
            return overallGrade.getGpaValue();
        }
    }
    
    /**
     * 计算最终分数
     */
    private double calculateFinalScore(SortGrade overallGrade, double overallGpa, 
                                     Map<SortCategory, CategoryScore> categoryScores) {
        // 基础分数：等级分数 * 1000 + GPA分数 * 100
        double baseScore = overallGrade.getGpaValue() * 1000 + overallGpa * 100;
        
        // 添加细微差异：各大类分数的加权和
        double detailScore = categoryScores.values().stream()
                .filter(CategoryScore::isSuccessful)
                .mapToDouble(CategoryScore::getWeightedScore)
                .sum();
        
        return baseScore + detailScore;
    }
    
    /**
     * 计算细分分数
     */
    private double calculateFineGrainedScore(Map<SortCategory, CategoryScore> categoryScores, 
                                           Map<SortCategory, Double> weights) {
        return categoryScores.entrySet().stream()
                .filter(entry -> entry.getValue().isSuccessful())
                .mapToDouble(entry -> {
                    CategoryScore score = entry.getValue();
                    double weight = weights.getOrDefault(entry.getKey(), 0.0);
                    return score.getRawScore() * weight;
                })
                .sum();
    }
    
    /**
     * 获取适用的大类权重
     */
    private Map<SortCategory, Double> getCategoryWeights(SortContext context) {
        Integer cityId = context.getDspOrder().getCityId();
        String timeSlot = getCurrentTimeSlot(context);
        return config.getCategoryWeights(cityId, timeSlot);
    }
    
    /**
     * 获取当前时段
     */
    private String getCurrentTimeSlot(SortContext context) {
        // 简化实现，实际可以根据订单时间判断是否为高峰期
        Calendar cal = Calendar.getInstance();
        cal.setTime(context.getDspOrder().getEstimatedUseTime());
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        
        if ((hour >= 7 && hour <= 9) || (hour >= 17 && hour <= 19)) {
            return "PEAK";
        } else if (hour >= 22 || hour <= 6) {
            return "NIGHT";
        } else {
            return "OFF_PEAK";
        }
    }
}
