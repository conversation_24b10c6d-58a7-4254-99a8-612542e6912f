package com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.FeatureConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.NormalizationType;

import java.util.List;

/**
 * 分层排序归一化器接口
 * 定义各种归一化算法的统一接口
 * 
 * <AUTHOR> Assistant
 */
public interface HierarchicalNormalizer {
    
    /**
     * 对数据进行归一化
     * @param values 原始数据列表
     * @param config 特征配置
     * @return 归一化后的数据列表
     */
    List<Double> normalize(List<Double> values, FeatureConfig config);
    
    /**
     * 对单个值进行归一化
     * @param value 原始值
     * @param statistics 统计信息
     * @param config 特征配置
     * @return 归一化后的值
     */
    double normalize(double value, NormalizationStatistics statistics, FeatureConfig config);
    
    /**
     * 获取支持的归一化类型
     * @return 归一化类型
     */
    NormalizationType getSupportedType();
    
    /**
     * 判断是否需要预计算统计信息
     * @return true if 需要统计信息
     */
    boolean requiresStatistics();
    
    /**
     * 验证配置参数的有效性
     * @param config 特征配置
     * @return true if 配置有效
     */
    default boolean validateConfig(FeatureConfig config) {
        return config != null && config.getNormalizationType() == getSupportedType();
    }
    
    /**
     * 处理异常值
     * @param values 原始数据
     * @param statistics 统计信息
     * @param config 特征配置
     * @return 处理后的数据
     */
    default List<Double> handleOutliers(List<Double> values, NormalizationStatistics statistics, FeatureConfig config) {
        // 默认不处理异常值，子类可以重写
        return values;
    }
    
    /**
     * 获取归一化后的数据范围
     * @return 数据范围描述
     */
    default String getOutputRange() {
        return "[0, 1]";
    }
}
