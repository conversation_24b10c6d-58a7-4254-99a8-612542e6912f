package com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.impl;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.FeatureConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.NormalizationType;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.HierarchicalNormalizer;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.NormalizationStatistics;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Sigmoid归一化器
 * 使用S型曲线将数据映射到(0,1)区间
 * 公式：x' = 1 / (1 + e^(-slope * (x - center)))
 * 适用于需要平滑映射的情况
 * 
 * <AUTHOR> Assistant
 */
public class SigmoidNormalizer implements HierarchicalNormalizer {
    
    @Override
    public List<Double> normalize(List<Double> values, FeatureConfig config) {
        if (values == null || values.isEmpty()) {
            return values;
        }
        
        // 计算统计信息
        NormalizationStatistics statistics = NormalizationStatistics.fromValues(values);
        
        // 处理异常值（如果启用）
        List<Double> processedValues = values;
        if (config.getNormalizationParams() != null && 
            config.getNormalizationParams().isOutlierHandling()) {
            processedValues = handleOutliers(values, statistics, config);
            // 重新计算统计信息
            statistics = NormalizationStatistics.fromValues(processedValues);
        }
        
        // 执行归一化
        return processedValues.stream()
                .map(value -> normalize(value, statistics, config))
                .collect(Collectors.toList());
    }
    
    @Override
    public double normalize(double value, NormalizationStatistics statistics, FeatureConfig config) {
        // 获取Sigmoid参数
        double slope = getSigmoidSlope(config);
        double center = getSigmoidCenter(statistics, config);
        
        // Sigmoid函数
        double exponent = -slope * (value - center);
        
        // 防止数值溢出
        if (exponent > 700) {
            return 0.0;
        } else if (exponent < -700) {
            return 1.0;
        }
        
        return 1.0 / (1.0 + Math.exp(exponent));
    }
    
    @Override
    public NormalizationType getSupportedType() {
        return NormalizationType.SIGMOID;
    }
    
    @Override
    public boolean requiresStatistics() {
        return true; // 需要统计信息来确定中心点
    }
    
    @Override
    public List<Double> handleOutliers(List<Double> values, NormalizationStatistics statistics, FeatureConfig config) {
        if (config.getNormalizationParams() == null || !config.getNormalizationParams().isOutlierHandling()) {
            return values;
        }
        
        double threshold = config.getNormalizationParams().getOutlierThreshold();
        double mean = statistics.getMean();
        double stdDev = statistics.getStdDev();
        
        // 对于Sigmoid归一化，异常值的影响相对较小，但仍可以进行处理
        return values.stream()
                .map(value -> {
                    if (Math.abs(value - mean) > threshold * stdDev) {
                        // 将异常值调整到阈值边界
                        if (value > mean) {
                            return mean + threshold * stdDev;
                        } else {
                            return mean - threshold * stdDev;
                        }
                    }
                    return value;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 获取Sigmoid斜率参数
     */
    private double getSigmoidSlope(FeatureConfig config) {
        if (config.getNormalizationParams() != null) {
            return config.getNormalizationParams().getSigmoidSlope();
        }
        return 1.0; // 默认斜率
    }
    
    /**
     * 获取Sigmoid中心点
     */
    private double getSigmoidCenter(NormalizationStatistics statistics, FeatureConfig config) {
        if (config.getNormalizationParams() != null && 
            config.getNormalizationParams().getSigmoidCenter() != 0.0) {
            return config.getNormalizationParams().getSigmoidCenter();
        }
        // 默认使用均值作为中心点
        return statistics.getMean();
    }
    
    /**
     * 创建自适应Sigmoid归一化器
     * 根据数据分布自动调整参数
     */
    public static SigmoidNormalizer createAdaptive() {
        return new SigmoidNormalizer() {
            @Override
            public double normalize(double value, NormalizationStatistics statistics, FeatureConfig config) {
                // 自适应调整斜率：根据标准差调整
                double adaptiveSlope = statistics.getStdDev() > 0 ? 1.0 / statistics.getStdDev() : 1.0;
                double center = statistics.getMean();
                
                double exponent = -adaptiveSlope * (value - center);
                
                if (exponent > 700) {
                    return 0.0;
                } else if (exponent < -700) {
                    return 1.0;
                }
                
                return 1.0 / (1.0 + Math.exp(exponent));
            }
        };
    }
    
    @Override
    public String getOutputRange() {
        return "(0, 1) S型曲线映射";
    }
}
