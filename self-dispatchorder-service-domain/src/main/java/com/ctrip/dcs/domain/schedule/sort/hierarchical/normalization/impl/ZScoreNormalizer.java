package com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.impl;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.FeatureConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.NormalizationType;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.HierarchicalNormalizer;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.NormalizationStatistics;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Z-Score标准化归一化器
 * 将数据标准化为均值0、标准差1的分布
 * 公式：x' = (x - μ) / σ
 * 适用于符合正态分布的数据
 * 
 * <AUTHOR> Assistant
 */
public class ZScoreNormalizer implements HierarchicalNormalizer {
    
    @Override
    public List<Double> normalize(List<Double> values, FeatureConfig config) {
        if (values == null || values.isEmpty()) {
            return values;
        }
        
        // 计算统计信息
        NormalizationStatistics statistics = NormalizationStatistics.fromValues(values);
        
        // 处理异常值（如果启用）
        List<Double> processedValues = values;
        if (config.getNormalizationParams() != null && 
            config.getNormalizationParams().isOutlierHandling()) {
            processedValues = handleOutliers(values, statistics, config);
            // 重新计算统计信息
            statistics = NormalizationStatistics.fromValues(processedValues);
        }
        
        // 执行归一化
        return processedValues.stream()
                .map(value -> normalize(value, statistics, config))
                .collect(Collectors.toList());
    }
    
    @Override
    public double normalize(double value, NormalizationStatistics statistics, FeatureConfig config) {
        // 获取均值和标准差
        double mean = getMean(statistics, config);
        double stdDev = getStdDev(statistics, config);
        
        // 处理标准差为0的情况
        if (Math.abs(stdDev) < 1e-10) {
            return 0.0; // 常数值标准化为0
        }
        
        // Z-Score标准化
        double zScore = (value - mean) / stdDev;
        
        // 可选：将Z-Score映射到[0,1]区间
        // 使用sigmoid函数或者简单的线性映射
        return mapZScoreToUnitInterval(zScore);
    }
    
    @Override
    public NormalizationType getSupportedType() {
        return NormalizationType.Z_SCORE;
    }
    
    @Override
    public boolean requiresStatistics() {
        return true;
    }
    
    @Override
    public List<Double> handleOutliers(List<Double> values, NormalizationStatistics statistics, FeatureConfig config) {
        if (config.getNormalizationParams() == null || !config.getNormalizationParams().isOutlierHandling()) {
            return values;
        }
        
        double threshold = config.getNormalizationParams().getOutlierThreshold();
        double mean = statistics.getMean();
        double stdDev = statistics.getStdDev();
        
        // 移除或调整异常值
        return values.stream()
                .map(value -> {
                    double zScore = Math.abs(value - mean) / stdDev;
                    if (zScore > threshold) {
                        // 将异常值调整到阈值边界
                        if (value > mean) {
                            return mean + threshold * stdDev;
                        } else {
                            return mean - threshold * stdDev;
                        }
                    }
                    return value;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 获取均值（支持自定义）
     */
    private double getMean(NormalizationStatistics statistics, FeatureConfig config) {
        if (config.getNormalizationParams() != null && 
            config.getNormalizationParams().getCustomMean() != null) {
            return config.getNormalizationParams().getCustomMean();
        }
        return statistics.getMean();
    }
    
    /**
     * 获取标准差（支持自定义）
     */
    private double getStdDev(NormalizationStatistics statistics, FeatureConfig config) {
        if (config.getNormalizationParams() != null && 
            config.getNormalizationParams().getCustomStdDev() != null) {
            return config.getNormalizationParams().getCustomStdDev();
        }
        return statistics.getStdDev();
    }
    
    /**
     * 将Z-Score映射到[0,1]区间
     * 使用sigmoid函数进行映射
     */
    private double mapZScoreToUnitInterval(double zScore) {
        // 使用sigmoid函数：f(x) = 1 / (1 + e^(-x))
        // 这样可以将(-∞, +∞)映射到(0, 1)
        return 1.0 / (1.0 + Math.exp(-zScore));
    }
    
    /**
     * 获取原始Z-Score值（不映射到[0,1]）
     * 用于需要保持Z-Score原始含义的场景
     */
    public double getRawZScore(double value, NormalizationStatistics statistics, FeatureConfig config) {
        double mean = getMean(statistics, config);
        double stdDev = getStdDev(statistics, config);
        
        if (Math.abs(stdDev) < 1e-10) {
            return 0.0;
        }
        
        return (value - mean) / stdDev;
    }
    
    @Override
    public String getOutputRange() {
        return "(0, 1) via Sigmoid mapping";
    }
}
