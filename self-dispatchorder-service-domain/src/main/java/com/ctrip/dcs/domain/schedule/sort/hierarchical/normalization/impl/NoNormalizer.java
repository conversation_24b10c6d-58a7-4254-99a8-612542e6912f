package com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.impl;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.FeatureConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.NormalizationType;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.HierarchicalNormalizer;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.NormalizationStatistics;

import java.util.List;

/**
 * 无归一化器
 * 直接使用原始值，不进行任何变换
 * 适用于已经是标准化分数的情况
 * 
 * <AUTHOR> Assistant
 */
public class NoNormalizer implements HierarchicalNormalizer {
    
    @Override
    public List<Double> normalize(List<Double> values, FeatureConfig config) {
        // 直接返回原始值，不做任何处理
        return values;
    }
    
    @Override
    public double normalize(double value, NormalizationStatistics statistics, FeatureConfig config) {
        // 直接返回原始值
        return value;
    }
    
    @Override
    public NormalizationType getSupportedType() {
        return NormalizationType.NONE;
    }
    
    @Override
    public boolean requiresStatistics() {
        return false; // 不需要统计信息
    }
    
    @Override
    public String getOutputRange() {
        return "原始值范围（无变换）";
    }
}
