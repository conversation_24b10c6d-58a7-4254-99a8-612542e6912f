package com.ctrip.dcs.domain.schedule.sort.hierarchical.config;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.AggregationStrategy;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortGrade;
import lombok.Builder;
import lombok.Getter;

import java.util.Map;

/**
 * 分层排序配置类
 * 定义分层排序的各种配置参数，包括大类权重、等级阈值、聚合策略等
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Builder
public class HierarchicalSortConfig {
    
    /**
     * 配置ID，用于标识不同的排序配置
     */
    private String configId;
    
    /**
     * 配置名称
     */
    private String configName;
    
    /**
     * 配置描述
     */
    private String description;
    
    /**
     * 是否启用分层排序
     * 如果为false，则降级到传统排序方式
     */
    @Builder.Default
    private boolean enabled = true;
    
    /**
     * 大类权重配置
     * key: SortCategory
     * value: 权重值 (0.0 - 1.0)
     * 所有权重之和应该等于1.0
     */
    private Map<SortCategory, Double> categoryWeights;
    
    /**
     * 特征项配置
     * key: 特征项ID (如 "F2", "F9", "F10" 等)
     * value: 特征项配置
     */
    private Map<String, FeatureConfig> featureConfigs;
    
    /**
     * 等级阈值配置
     * 自定义各等级的分数阈值，如果不配置则使用默认值
     * key: SortGrade
     * value: 最低分数阈值
     */
    private Map<SortGrade, Double> gradeThresholds;
    
    /**
     * 聚合策略
     * 定义如何将多个大类的等级聚合为最终等级
     */
    @Builder.Default
    private AggregationStrategy aggregationStrategy = AggregationStrategy.WEIGHTED_AVERAGE_GPA;
    
    /**
     * 是否启用细分打分
     * 当等级相同时，是否使用原始分数进行细分排序
     */
    @Builder.Default
    private boolean enableFineGrainedScoring = true;
    
    /**
     * 降级阈值
     * 当分层排序计算失败的比例超过此阈值时，自动降级到传统排序
     */
    @Builder.Default
    private double fallbackThreshold = 0.1;
    
    /**
     * 是否启用监控
     */
    @Builder.Default
    private boolean monitoringEnabled = true;
    
    /**
     * 城市特定配置
     * 不同城市可能有不同的权重配置
     * key: 城市ID
     * value: 城市特定的权重配置
     */
    private Map<Integer, Map<SortCategory, Double>> citySpecificWeights;
    
    /**
     * 时段特定配置
     * 不同时段可能有不同的权重配置
     * key: 时段标识 (如 "PEAK", "OFF_PEAK", "NIGHT")
     * value: 时段特定的权重配置
     */
    private Map<String, Map<SortCategory, Double>> timeSpecificWeights;
    
    /**
     * 获取指定城市的大类权重
     * @param cityId 城市ID
     * @return 大类权重配置，如果没有城市特定配置则返回默认配置
     */
    public Map<SortCategory, Double> getCategoryWeights(Integer cityId) {
        if (citySpecificWeights != null && citySpecificWeights.containsKey(cityId)) {
            return citySpecificWeights.get(cityId);
        }
        return categoryWeights;
    }
    
    /**
     * 获取指定时段的大类权重
     * @param timeSlot 时段标识
     * @return 大类权重配置，如果没有时段特定配置则返回默认配置
     */
    public Map<SortCategory, Double> getCategoryWeights(String timeSlot) {
        if (timeSpecificWeights != null && timeSpecificWeights.containsKey(timeSlot)) {
            return timeSpecificWeights.get(timeSlot);
        }
        return categoryWeights;
    }
    
    /**
     * 获取指定城市和时段的大类权重
     * @param cityId 城市ID
     * @param timeSlot 时段标识
     * @return 大类权重配置，优先级：时段特定 > 城市特定 > 默认配置
     */
    public Map<SortCategory, Double> getCategoryWeights(Integer cityId, String timeSlot) {
        // 优先使用时段特定配置
        if (timeSpecificWeights != null && timeSpecificWeights.containsKey(timeSlot)) {
            return timeSpecificWeights.get(timeSlot);
        }
        // 其次使用城市特定配置
        if (citySpecificWeights != null && citySpecificWeights.containsKey(cityId)) {
            return citySpecificWeights.get(cityId);
        }
        // 最后使用默认配置
        return categoryWeights;
    }
    
    /**
     * 验证配置的有效性
     * @return true if 配置有效
     */
    public boolean isValid() {
        // 检查配置ID
        if (configId == null || configId.trim().isEmpty()) {
            return false;
        }

        // 检查大类权重之和是否为1.0
        if (categoryWeights != null) {
            double totalWeight = categoryWeights.values().stream().mapToDouble(Double::doubleValue).sum();
            if (Math.abs(totalWeight - 1.0) > 0.001) {
                return false;
            }
        }

        // 检查特征项配置是否完整
        if (featureConfigs == null || featureConfigs.isEmpty()) {
            return false;
        }

        return true;
    }

    /**
     * 获取等级阈值配置
     * @return 等级阈值映射，如果没有自定义配置则返回默认值
     */
    public Map<String, Double> getGradeThresholds() {
        if (gradeThresholds != null && !gradeThresholds.isEmpty()) {
            Map<String, Double> result = new java.util.HashMap<>();
            gradeThresholds.forEach((grade, threshold) -> result.put(grade.getCode(), threshold));
            return result;
        }

        // 返回默认阈值
        Map<String, Double> defaultThresholds = new java.util.HashMap<>();
        defaultThresholds.put("A", 85.0);
        defaultThresholds.put("B", 70.0);
        defaultThresholds.put("C", 50.0);
        return defaultThresholds;
    }

    /**
     * 创建配置的副本构建器
     * @return 配置构建器
     */
    public HierarchicalSortConfigBuilder toBuilder() {
        return HierarchicalSortConfig.builder()
                .configId(this.configId)
                .configName(this.configName)
                .description(this.description)
                .enabled(this.enabled)
                .categoryWeights(this.categoryWeights)
                .featureConfigs(this.featureConfigs)
                .gradeThresholds(this.gradeThresholds)
                .aggregationStrategy(this.aggregationStrategy)
                .enableFineGrainedScoring(this.enableFineGrainedScoring)
                .fallbackThreshold(this.fallbackThreshold)
                .monitoringEnabled(this.monitoringEnabled)
                .citySpecificWeights(this.citySpecificWeights)
                .timeSpecificWeights(this.timeSpecificWeights);
    }
}
