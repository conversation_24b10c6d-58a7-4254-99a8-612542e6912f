package com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.impl;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.FeatureConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.NormalizationType;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.HierarchicalNormalizer;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.NormalizationStatistics;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 逆向Min-Max归一化器
 * 将数据逆向线性缩放到[0,1]区间，值越小分数越高
 * 公式：x' = (max - x) / (max - min)
 * 适用于空驶距离、等待时间等"越小越好"的指标
 * 
 * <AUTHOR> Assistant
 */
public class InverseMinMaxNormalizer implements HierarchicalNormalizer {
    
    @Override
    public List<Double> normalize(List<Double> values, FeatureConfig config) {
        if (values == null || values.isEmpty()) {
            return values;
        }
        
        // 计算统计信息
        NormalizationStatistics statistics = NormalizationStatistics.fromValues(values);
        
        // 处理异常值（如果启用）
        List<Double> processedValues = values;
        if (config.getNormalizationParams() != null && 
            config.getNormalizationParams().isOutlierHandling()) {
            processedValues = handleOutliers(values, statistics, config);
            // 重新计算统计信息
            statistics = NormalizationStatistics.fromValues(processedValues);
        }
        
        // 执行归一化
        return processedValues.stream()
                .map(value -> normalize(value, statistics, config))
                .collect(Collectors.toList());
    }
    
    @Override
    public double normalize(double value, NormalizationStatistics statistics, FeatureConfig config) {
        // 获取最小值和最大值
        double min = getMinValue(statistics, config);
        double max = getMaxValue(statistics, config);
        
        // 处理常数情况
        if (Math.abs(max - min) < 1e-10) {
            return 0.5; // 常数值归一化为0.5
        }
        
        // 逆向Min-Max归一化：值越小分数越高
        double normalized = (max - value) / (max - min);
        
        // 确保结果在[0,1]范围内
        return Math.max(0.0, Math.min(1.0, normalized));
    }
    
    @Override
    public NormalizationType getSupportedType() {
        return NormalizationType.INVERSE_MIN_MAX;
    }
    
    @Override
    public boolean requiresStatistics() {
        return true;
    }
    
    @Override
    public List<Double> handleOutliers(List<Double> values, NormalizationStatistics statistics, FeatureConfig config) {
        if (config.getNormalizationParams() == null || !config.getNormalizationParams().isOutlierHandling()) {
            return values;
        }
        
        double threshold = config.getNormalizationParams().getOutlierThreshold();
        double mean = statistics.getMean();
        double stdDev = statistics.getStdDev();
        
        // 对于逆向归一化，异常值处理需要特别考虑
        // 过大的值（异常高值）对逆向归一化影响更大
        return values.stream()
                .map(value -> {
                    if (Math.abs(value - mean) > threshold * stdDev) {
                        // 将异常值调整到阈值边界
                        if (value > mean) {
                            return mean + threshold * stdDev;
                        } else {
                            return mean - threshold * stdDev;
                        }
                    }
                    return value;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 获取最小值（支持自定义）
     */
    private double getMinValue(NormalizationStatistics statistics, FeatureConfig config) {
        if (config.getNormalizationParams() != null && 
            config.getNormalizationParams().getCustomMin() != null) {
            return config.getNormalizationParams().getCustomMin();
        }
        return statistics.getMin();
    }
    
    /**
     * 获取最大值（支持自定义）
     */
    private double getMaxValue(NormalizationStatistics statistics, FeatureConfig config) {
        if (config.getNormalizationParams() != null && 
            config.getNormalizationParams().getCustomMax() != null) {
            return config.getNormalizationParams().getCustomMax();
        }
        return statistics.getMax();
    }
    
    @Override
    public String getOutputRange() {
        return "[0, 1] (逆向：值越小分数越高)";
    }
}
