package com.ctrip.dcs.domain.schedule.sort.hierarchical.monitor;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.HierarchicalSortConfig;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 分层排序健康检查器
 * 定期检查分层排序系统的健康状态，并在必要时触发降级
 * 
 * <AUTHOR> Assistant
 */
public class HierarchicalSortHealthChecker {
    
    private static final Logger logger = LoggerFactory.getLogger(HierarchicalSortHealthChecker.class);
    
    private final HierarchicalSortConfig config;
    private final HierarchicalSortMonitor monitor;
    private final ScheduledExecutorService scheduler;
    private final AtomicBoolean isHealthy = new AtomicBoolean(true);
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    
    // 健康检查配置
    private final long checkIntervalSeconds;
    private final double minSuccessRate;
    private final long maxProcessingTime;
    private final int maxConsecutiveFailures;
    
    // 状态跟踪
    private int consecutiveFailures = 0;
    private long lastHealthyTime = System.currentTimeMillis();
    
    public HierarchicalSortHealthChecker(HierarchicalSortConfig config, HierarchicalSortMonitor monitor) {
        this.config = config;
        this.monitor = monitor;
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "HierarchicalSort-HealthChecker");
            t.setDaemon(true);
            return t;
        });
        
        // 健康检查配置
        this.checkIntervalSeconds = 30; // 30秒检查一次
        this.minSuccessRate = 1.0 - config.getFallbackThreshold(); // 最小成功率
        this.maxProcessingTime = 5000; // 最大处理时间5秒
        this.maxConsecutiveFailures = 3; // 最大连续失败次数
    }
    
    /**
     * 启动健康检查
     */
    public void start() {
        if (isRunning.compareAndSet(false, true)) {
            scheduler.scheduleAtFixedRate(this::performHealthCheck, 
                    checkIntervalSeconds, checkIntervalSeconds, TimeUnit.SECONDS);
            logger.info("HierarchicalSort_HealthChecker", "健康检查器已启动");
        }
    }
    
    /**
     * 停止健康检查
     */
    public void stop() {
        if (isRunning.compareAndSet(true, false)) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
            logger.info("HierarchicalSort_HealthChecker", "健康检查器已停止");
        }
    }
    
    /**
     * 执行健康检查
     */
    private void performHealthCheck() {
        try {
            boolean currentlyHealthy = checkHealth();
            boolean wasHealthy = isHealthy.get();
            
            if (currentlyHealthy != wasHealthy) {
                isHealthy.set(currentlyHealthy);
                
                if (currentlyHealthy) {
                    onHealthRecovered();
                } else {
                    onHealthDegraded();
                }
            }
            
            if (currentlyHealthy) {
                consecutiveFailures = 0;
                lastHealthyTime = System.currentTimeMillis();
            } else {
                consecutiveFailures++;
            }
            
            // 记录健康检查结果
            logger.info("HierarchicalSort_HealthCheck", 
                    String.format("健康状态: %s, 连续失败: %d, 成功率: %.2f%%", 
                            currentlyHealthy ? "健康" : "异常", consecutiveFailures, 
                            monitor.getSuccessRate() * 100));
                            
        } catch (Exception e) {
            logger.error("健康检查执行失败", e);
            consecutiveFailures++;
            isHealthy.set(false);
        }
    }
    
    /**
     * 检查健康状态
     */
    private boolean checkHealth() {
        // 检查配置是否启用
        if (!config.isEnabled()) {
            return false;
        }
        
        // 检查监控器状态
        if (!monitor.isHealthy()) {
            return false;
        }
        
        // 检查成功率
        double successRate = monitor.getSuccessRate();
        if (successRate < minSuccessRate && monitor.getTotalSortCount() > 10) {
            logger.warn("HierarchicalSort_HealthCheck", 
                    String.format("成功率过低: %.2f%% < %.2f%%", successRate * 100, minSuccessRate * 100));
            return false;
        }
        
        // 检查平均处理时间
        double avgProcessingTime = monitor.getAverageProcessingTime();
        if (avgProcessingTime > maxProcessingTime && monitor.getTotalSortCount() > 5) {
            logger.warn("HierarchicalSort_HealthCheck", 
                    String.format("平均处理时间过长: %.2f ms > %d ms", avgProcessingTime, maxProcessingTime));
            return false;
        }
        
        // 检查连续失败次数
        if (consecutiveFailures >= maxConsecutiveFailures) {
            logger.warn("HierarchicalSort_HealthCheck", 
                    String.format("连续失败次数过多: %d >= %d", consecutiveFailures, maxConsecutiveFailures));
            return false;
        }
        
        // 检查最近是否有成功记录
        long timeSinceLastSuccess = System.currentTimeMillis() - monitor.getLastSuccessTime();
        if (monitor.getLastSuccessTime() > 0 && timeSinceLastSuccess > 600000) { // 10分钟
            logger.warn("HierarchicalSort_HealthCheck", 
                    String.format("最近无成功记录: %d ms", timeSinceLastSuccess));
            return false;
        }
        
        return true;
    }
    
    /**
     * 健康状态恢复处理
     */
    private void onHealthRecovered() {
        logger.info("HierarchicalSort_HealthRecovered", 
                String.format("分层排序健康状态已恢复，连续失败次数: %d", consecutiveFailures));
        
        // 重置失败计数
        consecutiveFailures = 0;
        
        // 可以在这里添加健康恢复的通知逻辑
        // 例如：发送恢复通知、更新配置等
    }
    
    /**
     * 健康状态降级处理
     */
    private void onHealthDegraded() {
        logger.warn("HierarchicalSort_HealthDegraded", 
                String.format("分层排序健康状态降级，连续失败次数: %d", consecutiveFailures));
        
        // 可以在这里添加降级处理逻辑
        // 例如：发送告警、自动禁用分层排序等
        
        // 如果连续失败次数过多，可以考虑自动禁用
        if (consecutiveFailures >= maxConsecutiveFailures * 2) {
            logger.error("HierarchicalSort_AutoDisable", 
                    "连续失败次数过多，建议禁用分层排序");
            // 这里可以添加自动禁用逻辑
        }
    }
    
    /**
     * 手动触发健康检查
     * @return 当前健康状态
     */
    public boolean checkHealthNow() {
        performHealthCheck();
        return isHealthy.get();
    }
    
    /**
     * 获取当前健康状态
     * @return true if 健康
     */
    public boolean isHealthy() {
        return isHealthy.get();
    }
    
    /**
     * 获取连续失败次数
     * @return 连续失败次数
     */
    public int getConsecutiveFailures() {
        return consecutiveFailures;
    }
    
    /**
     * 获取最后健康时间
     * @return 最后健康时间戳
     */
    public long getLastHealthyTime() {
        return lastHealthyTime;
    }
    
    /**
     * 获取健康检查状态报告
     * @return 状态报告
     */
    public String getHealthReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 分层排序健康检查报告 ===\n");
        report.append(String.format("当前状态: %s\n", isHealthy.get() ? "健康" : "异常"));
        report.append(String.format("检查器运行: %s\n", isRunning.get() ? "运行中" : "已停止"));
        report.append(String.format("连续失败次数: %d\n", consecutiveFailures));
        report.append(String.format("最后健康时间: %d\n", lastHealthyTime));
        report.append(String.format("检查间隔: %d 秒\n", checkIntervalSeconds));
        report.append(String.format("最小成功率: %.2f%%\n", minSuccessRate * 100));
        report.append(String.format("最大处理时间: %d ms\n", maxProcessingTime));
        report.append(String.format("最大连续失败: %d\n", maxConsecutiveFailures));
        
        return report.toString();
    }
    
    /**
     * 重置健康状态
     */
    public void reset() {
        consecutiveFailures = 0;
        isHealthy.set(true);
        lastHealthyTime = System.currentTimeMillis();
        logger.info("HierarchicalSort_HealthChecker", "健康检查器状态已重置");
    }
}
