package com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.impl;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.FeatureConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.NormalizationType;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.HierarchicalNormalizer;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.NormalizationStatistics;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 排名归一化器
 * 基于排名的分位数归一化，关注相对排名而非绝对值
 * 公式：x' = rank(x) / N
 * 适用于关注相对位置的场景
 * 
 * <AUTHOR> Assistant
 */
public class RankNormalizer implements HierarchicalNormalizer {
    
    @Override
    public List<Double> normalize(List<Double> values, FeatureConfig config) {
        if (values == null || values.isEmpty()) {
            return values;
        }
        
        // 处理异常值（如果启用）
        List<Double> processedValues = values;
        if (config.getNormalizationParams() != null && 
            config.getNormalizationParams().isOutlierHandling()) {
            NormalizationStatistics statistics = NormalizationStatistics.fromValues(values);
            processedValues = handleOutliers(values, statistics, config);
        }
        
        // 创建值到索引的映射
        Map<Double, List<Integer>> valueToIndices = new HashMap<>();
        for (int i = 0; i < processedValues.size(); i++) {
            double value = processedValues.get(i);
            valueToIndices.computeIfAbsent(value, k -> new ArrayList<>()).add(i);
        }
        
        // 对值进行排序
        List<Double> sortedValues = processedValues.stream()
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        
        // 计算每个值的排名
        Map<Double, Double> valueToRank = new HashMap<>();
        for (int i = 0; i < sortedValues.size(); i++) {
            double value = sortedValues.get(i);
            List<Integer> indices = valueToIndices.get(value);
            
            // 处理相同值的情况：使用平均排名
            double averageRank = indices.stream()
                    .mapToInt(idx -> processedValues.indexOf(value))
                    .average()
                    .orElse(i);
            
            valueToRank.put(value, averageRank);
        }
        
        // 归一化排名
        int totalCount = processedValues.size();
        return processedValues.stream()
                .map(value -> {
                    double rank = valueToRank.get(value);
                    return rank / (totalCount - 1); // 归一化到[0,1]
                })
                .collect(Collectors.toList());
    }
    
    @Override
    public double normalize(double value, NormalizationStatistics statistics, FeatureConfig config) {
        // 单个值的排名归一化需要额外的上下文信息
        // 这里提供一个简化的实现，实际使用时建议使用批量方法
        throw new UnsupportedOperationException(
            "排名归一化需要完整的数据集，请使用 normalize(List<Double>, FeatureConfig) 方法");
    }
    
    @Override
    public NormalizationType getSupportedType() {
        return NormalizationType.RANK_NORMALIZATION;
    }
    
    @Override
    public boolean requiresStatistics() {
        return false; // 排名归一化不需要传统的统计信息
    }
    
    @Override
    public List<Double> handleOutliers(List<Double> values, NormalizationStatistics statistics, FeatureConfig config) {
        if (config.getNormalizationParams() == null || !config.getNormalizationParams().isOutlierHandling()) {
            return values;
        }
        
        // 对于排名归一化，异常值处理可以通过截断极值来实现
        double threshold = config.getNormalizationParams().getOutlierThreshold();
        double mean = statistics.getMean();
        double stdDev = statistics.getStdDev();
        
        double lowerBound = mean - threshold * stdDev;
        double upperBound = mean + threshold * stdDev;
        
        return values.stream()
                .map(value -> Math.max(lowerBound, Math.min(upperBound, value)))
                .collect(Collectors.toList());
    }
    
    /**
     * 计算分位数排名
     * @param values 数据列表
     * @param percentiles 分位数列表 (0.0 - 1.0)
     * @return 分位数值映射
     */
    public Map<Double, Double> calculatePercentiles(List<Double> values, List<Double> percentiles) {
        if (values == null || values.isEmpty() || percentiles == null || percentiles.isEmpty()) {
            return new HashMap<>();
        }
        
        List<Double> sortedValues = values.stream().sorted().collect(Collectors.toList());
        Map<Double, Double> result = new HashMap<>();
        
        for (double percentile : percentiles) {
            if (percentile < 0.0 || percentile > 1.0) {
                continue;
            }
            
            double index = percentile * (sortedValues.size() - 1);
            int lowerIndex = (int) Math.floor(index);
            int upperIndex = (int) Math.ceil(index);
            
            if (lowerIndex == upperIndex) {
                result.put(percentile, sortedValues.get(lowerIndex));
            } else {
                double lowerValue = sortedValues.get(lowerIndex);
                double upperValue = sortedValues.get(upperIndex);
                double interpolatedValue = lowerValue + (index - lowerIndex) * (upperValue - lowerValue);
                result.put(percentile, interpolatedValue);
            }
        }
        
        return result;
    }
    
    /**
     * 获取值的分位数排名
     * @param value 目标值
     * @param values 完整数据集
     * @return 分位数排名 [0.0, 1.0]
     */
    public double getPercentileRank(double value, List<Double> values) {
        if (values == null || values.isEmpty()) {
            return 0.0;
        }
        
        long countBelow = values.stream().mapToLong(v -> v < value ? 1 : 0).sum();
        long countEqual = values.stream().mapToLong(v -> v == value ? 1 : 0).sum();
        
        // 使用平均排名处理相同值
        return (countBelow + countEqual / 2.0) / values.size();
    }
    
    @Override
    public String getOutputRange() {
        return "[0, 1] 基于排名的分位数";
    }
}
