package com.ctrip.dcs.domain.schedule.sort.hierarchical.config;

import lombok.Builder;
import lombok.Getter;

/**
 * 归一化参数配置类
 * 用于存储特定归一化方式需要的参数
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Builder
public class NormalizationParams {
    
    /**
     * 自定义最小值
     * 用于Min-Max归一化时指定固定的最小值
     */
    private Double customMin;
    
    /**
     * 自定义最大值
     * 用于Min-Max归一化时指定固定的最大值
     */
    private Double customMax;
    
    /**
     * 对数归一化的底数
     * 默认为自然对数e
     */
    @Builder.Default
    private Double logBase = Math.E;
    
    /**
     * Sigmoid函数的斜率参数
     * 控制S型曲线的陡峭程度
     */
    @Builder.Default
    private Double sigmoidSlope = 1.0;
    
    /**
     * Sigmoid函数的中心点
     * 控制S型曲线的中心位置
     */
    @Builder.Default
    private Double sigmoidCenter = 0.0;
    
    /**
     * Z-Score归一化的标准差倍数
     * 用于控制归一化后的数据范围
     */
    @Builder.Default
    private Double zScoreMultiplier = 3.0;
    
    /**
     * 是否启用异常值处理
     */
    @Builder.Default
    private boolean outlierHandling = false;
    
    /**
     * 异常值阈值（标准差倍数）
     * 超过此阈值的值被认为是异常值
     */
    @Builder.Default
    private Double outlierThreshold = 3.0;
    
    /**
     * 异常值处理方式
     * CLIP: 截断到阈值边界
     * REMOVE: 移除异常值
     * WINSORIZE: 用阈值边界值替换
     */
    @Builder.Default
    private OutlierHandlingMethod outlierHandlingMethod = OutlierHandlingMethod.CLIP;
    
    /**
     * 排名归一化的分位数方法
     * UNIFORM: 均匀分布
     * NORMAL: 正态分布
     */
    @Builder.Default
    private RankNormalizationMethod rankMethod = RankNormalizationMethod.UNIFORM;
    
    /**
     * 是否使用平滑处理
     * 对于某些归一化方法，可以启用平滑处理以减少噪声
     */
    @Builder.Default
    private boolean smoothing = false;
    
    /**
     * 平滑窗口大小
     * 用于移动平均等平滑算法
     */
    @Builder.Default
    private Integer smoothingWindow = 3;
    
    /**
     * 异常值处理方式枚举
     */
    public enum OutlierHandlingMethod {
        CLIP,       // 截断
        REMOVE,     // 移除
        WINSORIZE   // 替换
    }
    
    /**
     * 排名归一化方法枚举
     */
    public enum RankNormalizationMethod {
        UNIFORM,    // 均匀分布
        NORMAL      // 正态分布
    }
    
    /**
     * 验证参数的有效性
     * @return true if 参数有效
     */
    public boolean isValid() {
        // 检查基本参数
        if (logBase != null && logBase <= 0) {
            return false;
        }
        
        if (sigmoidSlope != null && sigmoidSlope <= 0) {
            return false;
        }
        
        if (zScoreMultiplier != null && zScoreMultiplier <= 0) {
            return false;
        }
        
        if (outlierThreshold != null && outlierThreshold <= 0) {
            return false;
        }
        
        if (smoothingWindow != null && smoothingWindow <= 0) {
            return false;
        }
        
        // 检查自定义范围
        if (customMin != null && customMax != null && customMin >= customMax) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 创建默认参数
     * @return 默认归一化参数
     */
    public static NormalizationParams createDefault() {
        return NormalizationParams.builder().build();
    }
    
    /**
     * 创建Min-Max归一化参数
     * @param min 最小值
     * @param max 最大值
     * @return Min-Max归一化参数
     */
    public static NormalizationParams createMinMax(double min, double max) {
        return NormalizationParams.builder()
                .customMin(min)
                .customMax(max)
                .build();
    }
    
    /**
     * 创建对数归一化参数
     * @param base 对数底数
     * @return 对数归一化参数
     */
    public static NormalizationParams createLog(double base) {
        return NormalizationParams.builder()
                .logBase(base)
                .build();
    }
    
    /**
     * 创建Sigmoid归一化参数
     * @param slope 斜率
     * @param center 中心点
     * @return Sigmoid归一化参数
     */
    public static NormalizationParams createSigmoid(double slope, double center) {
        return NormalizationParams.builder()
                .sigmoidSlope(slope)
                .sigmoidCenter(center)
                .build();
    }
    
    /**
     * 创建带异常值处理的参数
     * @param threshold 异常值阈值
     * @param method 处理方式
     * @return 带异常值处理的参数
     */
    public static NormalizationParams createWithOutlierHandling(double threshold, OutlierHandlingMethod method) {
        return NormalizationParams.builder()
                .outlierHandling(true)
                .outlierThreshold(threshold)
                .outlierHandlingMethod(method)
                .build();
    }
}
