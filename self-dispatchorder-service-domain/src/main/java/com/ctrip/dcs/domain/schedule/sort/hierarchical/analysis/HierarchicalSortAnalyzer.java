package com.ctrip.dcs.domain.schedule.sort.hierarchical.analysis;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortGrade;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.grade.CategoryScore;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.grade.HierarchicalScore;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import lombok.Builder;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 分层排序分析器
 * 提供分层排序结果的统计分析功能
 * 
 * <AUTHOR> Assistant
 */
public class HierarchicalSortAnalyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(HierarchicalSortAnalyzer.class);
    
    /**
     * 分析分层排序结果
     * @param scores 分层排序分数列表
     * @return 分析结果
     */
    public AnalysisResult analyzeScores(List<HierarchicalScore> scores) {
        if (scores == null || scores.isEmpty()) {
            return AnalysisResult.empty();
        }
        
        // 过滤成功的分数
        List<HierarchicalScore> successfulScores = scores.stream()
                .filter(HierarchicalScore::isSuccessful)
                .collect(Collectors.toList());
        
        // 基本统计
        int totalCount = scores.size();
        int successfulCount = successfulScores.size();
        double successRate = (double) successfulCount / totalCount;
        
        // 等级分布统计
        Map<SortGrade, Long> gradeDistribution = successfulScores.stream()
                .collect(Collectors.groupingBy(HierarchicalScore::getOverallGrade, Collectors.counting()));
        
        // 大类分数统计
        Map<SortCategory, CategoryAnalysis> categoryAnalysis = analyzeCategoryScores(successfulScores);
        
        // GPA统计
        DoubleSummaryStatistics gpaStats = successfulScores.stream()
                .mapToDouble(HierarchicalScore::getOverallGpa)
                .summaryStatistics();
        
        // 最终分数统计
        DoubleSummaryStatistics finalScoreStats = successfulScores.stream()
                .mapToDouble(HierarchicalScore::getFinalScore)
                .summaryStatistics();
        
        // 性能统计
        DoubleSummaryStatistics computationTimeStats = successfulScores.stream()
                .mapToDouble(HierarchicalScore::getComputationTime)
                .summaryStatistics();
        
        // 降级统计
        long fallbackCount = scores.stream().mapToLong(score -> score.isFallback() ? 1 : 0).sum();
        double fallbackRate = (double) fallbackCount / totalCount;
        
        return AnalysisResult.builder()
                .totalCount(totalCount)
                .successfulCount(successfulCount)
                .successRate(successRate)
                .fallbackCount((int) fallbackCount)
                .fallbackRate(fallbackRate)
                .gradeDistribution(gradeDistribution)
                .categoryAnalysis(categoryAnalysis)
                .gpaStats(gpaStats)
                .finalScoreStats(finalScoreStats)
                .computationTimeStats(computationTimeStats)
                .build();
    }
    
    /**
     * 分析大类分数
     */
    private Map<SortCategory, CategoryAnalysis> analyzeCategoryScores(List<HierarchicalScore> scores) {
        Map<SortCategory, CategoryAnalysis> result = new HashMap<>();
        
        for (SortCategory category : SortCategory.values()) {
            List<CategoryScore> categoryScores = scores.stream()
                    .map(score -> score.getCategoryScore(category))
                    .filter(Objects::nonNull)
                    .filter(CategoryScore::isSuccessful)
                    .collect(Collectors.toList());
            
            if (!categoryScores.isEmpty()) {
                // 分数统计
                DoubleSummaryStatistics scoreStats = categoryScores.stream()
                        .mapToDouble(CategoryScore::getRawScore)
                        .summaryStatistics();
                
                // 等级分布
                Map<SortGrade, Long> gradeDistribution = categoryScores.stream()
                        .collect(Collectors.groupingBy(CategoryScore::getGrade, Collectors.counting()));
                
                // 权重统计
                DoubleSummaryStatistics weightStats = categoryScores.stream()
                        .mapToDouble(CategoryScore::getWeight)
                        .summaryStatistics();
                
                CategoryAnalysis analysis = CategoryAnalysis.builder()
                        .category(category)
                        .count(categoryScores.size())
                        .scoreStats(scoreStats)
                        .gradeDistribution(gradeDistribution)
                        .weightStats(weightStats)
                        .build();
                
                result.put(category, analysis);
            }
        }
        
        return result;
    }
    
    /**
     * 比较两组分层排序结果
     * @param baseline 基线结果
     * @param current 当前结果
     * @return 比较结果
     */
    public ComparisonResult compareResults(AnalysisResult baseline, AnalysisResult current) {
        if (baseline == null || current == null) {
            return ComparisonResult.empty();
        }
        
        // 成功率变化
        double successRateChange = current.getSuccessRate() - baseline.getSuccessRate();
        
        // 降级率变化
        double fallbackRateChange = current.getFallbackRate() - baseline.getFallbackRate();
        
        // GPA变化
        double gpaChange = current.getGpaStats().getAverage() - baseline.getGpaStats().getAverage();
        
        // 性能变化
        double performanceChange = current.getComputationTimeStats().getAverage() - 
                baseline.getComputationTimeStats().getAverage();
        
        // 等级分布变化
        Map<SortGrade, Double> gradeDistributionChange = new HashMap<>();
        for (SortGrade grade : SortGrade.values()) {
            double baselineRatio = getGradeRatio(baseline.getGradeDistribution(), grade, baseline.getSuccessfulCount());
            double currentRatio = getGradeRatio(current.getGradeDistribution(), grade, current.getSuccessfulCount());
            gradeDistributionChange.put(grade, currentRatio - baselineRatio);
        }
        
        return ComparisonResult.builder()
                .baseline(baseline)
                .current(current)
                .successRateChange(successRateChange)
                .fallbackRateChange(fallbackRateChange)
                .gpaChange(gpaChange)
                .performanceChange(performanceChange)
                .gradeDistributionChange(gradeDistributionChange)
                .build();
    }
    
    /**
     * 获取等级比例
     */
    private double getGradeRatio(Map<SortGrade, Long> distribution, SortGrade grade, int total) {
        if (total == 0) return 0.0;
        return distribution.getOrDefault(grade, 0L).doubleValue() / total;
    }
    
    /**
     * 生成分析报告
     * @param result 分析结果
     * @return 报告字符串
     */
    public String generateReport(AnalysisResult result) {
        StringBuilder report = new StringBuilder();
        report.append("=== 分层排序分析报告 ===\n");
        
        // 基本统计
        report.append(String.format("总司机数: %d\n", result.getTotalCount()));
        report.append(String.format("成功计算: %d (%.2f%%)\n", 
                result.getSuccessfulCount(), result.getSuccessRate() * 100));
        report.append(String.format("降级数量: %d (%.2f%%)\n", 
                result.getFallbackCount(), result.getFallbackRate() * 100));
        
        // 等级分布
        report.append("\n--- 等级分布 ---\n");
        for (SortGrade grade : SortGrade.values()) {
            long count = result.getGradeDistribution().getOrDefault(grade, 0L);
            double ratio = result.getSuccessfulCount() > 0 ? 
                    (double) count / result.getSuccessfulCount() * 100 : 0.0;
            report.append(String.format("%s级: %d (%.2f%%)\n", grade.getCode(), count, ratio));
        }
        
        // GPA统计
        report.append(String.format("\n--- GPA统计 ---\n"));
        report.append(String.format("平均GPA: %.3f\n", result.getGpaStats().getAverage()));
        report.append(String.format("GPA范围: %.3f - %.3f\n", 
                result.getGpaStats().getMin(), result.getGpaStats().getMax()));
        
        // 最终分数统计
        report.append(String.format("\n--- 最终分数统计 ---\n"));
        report.append(String.format("平均分数: %.2f\n", result.getFinalScoreStats().getAverage()));
        report.append(String.format("分数范围: %.2f - %.2f\n", 
                result.getFinalScoreStats().getMin(), result.getFinalScoreStats().getMax()));
        
        // 性能统计
        report.append(String.format("\n--- 性能统计 ---\n"));
        report.append(String.format("平均计算时间: %.2f ms\n", result.getComputationTimeStats().getAverage()));
        report.append(String.format("最大计算时间: %.2f ms\n", result.getComputationTimeStats().getMax()));
        
        // 大类分析
        report.append("\n--- 大类分析 ---\n");
        for (Map.Entry<SortCategory, CategoryAnalysis> entry : result.getCategoryAnalysis().entrySet()) {
            CategoryAnalysis analysis = entry.getValue();
            report.append(String.format("%s: 平均分%.2f, A级%.2f%%\n", 
                    analysis.getCategory().getDescription(),
                    analysis.getScoreStats().getAverage(),
                    getGradeRatio(analysis.getGradeDistribution(), SortGrade.A, analysis.getCount()) * 100));
        }
        
        return report.toString();
    }
    
    /**
     * 生成比较报告
     * @param comparison 比较结果
     * @return 比较报告
     */
    public String generateComparisonReport(ComparisonResult comparison) {
        StringBuilder report = new StringBuilder();
        report.append("=== 分层排序比较报告 ===\n");
        
        report.append(String.format("成功率变化: %+.2f%%\n", comparison.getSuccessRateChange() * 100));
        report.append(String.format("降级率变化: %+.2f%%\n", comparison.getFallbackRateChange() * 100));
        report.append(String.format("平均GPA变化: %+.3f\n", comparison.getGpaChange()));
        report.append(String.format("平均计算时间变化: %+.2f ms\n", comparison.getPerformanceChange()));
        
        report.append("\n--- 等级分布变化 ---\n");
        for (Map.Entry<SortGrade, Double> entry : comparison.getGradeDistributionChange().entrySet()) {
            report.append(String.format("%s级: %+.2f%%\n", 
                    entry.getKey().getCode(), entry.getValue() * 100));
        }
        
        return report.toString();
    }

    /**
     * 分析结果类
     */
    @Data
    @Builder
    public static class AnalysisResult {
        private int totalCount;
        private int successfulCount;
        private double successRate;
        private int fallbackCount;
        private double fallbackRate;
        private Map<SortGrade, Long> gradeDistribution;
        private Map<SortCategory, CategoryAnalysis> categoryAnalysis;
        private DoubleSummaryStatistics gpaStats;
        private DoubleSummaryStatistics finalScoreStats;
        private DoubleSummaryStatistics computationTimeStats;

        public static AnalysisResult empty() {
            return AnalysisResult.builder()
                    .totalCount(0)
                    .successfulCount(0)
                    .successRate(0.0)
                    .fallbackCount(0)
                    .fallbackRate(0.0)
                    .gradeDistribution(new HashMap<>())
                    .categoryAnalysis(new HashMap<>())
                    .gpaStats(new DoubleSummaryStatistics())
                    .finalScoreStats(new DoubleSummaryStatistics())
                    .computationTimeStats(new DoubleSummaryStatistics())
                    .build();
        }
    }

    /**
     * 大类分析结果
     */
    @Data
    @Builder
    public static class CategoryAnalysis {
        private SortCategory category;
        private int count;
        private DoubleSummaryStatistics scoreStats;
        private Map<SortGrade, Long> gradeDistribution;
        private DoubleSummaryStatistics weightStats;
    }

    /**
     * 比较结果类
     */
    @Data
    @Builder
    public static class ComparisonResult {
        private AnalysisResult baseline;
        private AnalysisResult current;
        private double successRateChange;
        private double fallbackRateChange;
        private double gpaChange;
        private double performanceChange;
        private Map<SortGrade, Double> gradeDistributionChange;

        public static ComparisonResult empty() {
            return ComparisonResult.builder()
                    .baseline(AnalysisResult.empty())
                    .current(AnalysisResult.empty())
                    .successRateChange(0.0)
                    .fallbackRateChange(0.0)
                    .gpaChange(0.0)
                    .performanceChange(0.0)
                    .gradeDistributionChange(new HashMap<>())
                    .build();
        }
    }
}
