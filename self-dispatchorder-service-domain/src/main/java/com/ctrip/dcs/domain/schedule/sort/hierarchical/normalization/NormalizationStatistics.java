package com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization;

import lombok.Builder;
import lombok.Getter;

import java.util.List;

/**
 * 归一化统计信息类
 * 存储归一化过程中需要的统计数据
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Builder
public class NormalizationStatistics {
    
    /**
     * 最小值
     */
    private double min;
    
    /**
     * 最大值
     */
    private double max;
    
    /**
     * 均值
     */
    private double mean;
    
    /**
     * 标准差
     */
    private double stdDev;
    
    /**
     * 中位数
     */
    private double median;
    
    /**
     * 数据总数
     */
    private int count;
    
    /**
     * 是否包含异常值
     */
    private boolean hasOutliers;
    
    /**
     * 异常值数量
     */
    private int outlierCount;
    
    /**
     * 从数据列表计算统计信息
     * @param values 数据列表
     * @param outlierThreshold 异常值阈值（标准差倍数）
     * @return 统计信息
     */
    public static NormalizationStatistics fromValues(List<Double> values, double outlierThreshold) {
        if (values == null || values.isEmpty()) {
            return NormalizationStatistics.builder()
                    .min(0.0)
                    .max(0.0)
                    .mean(0.0)
                    .stdDev(0.0)
                    .median(0.0)
                    .count(0)
                    .hasOutliers(false)
                    .outlierCount(0)
                    .build();
        }
        
        // 计算基本统计量
        double min = values.stream().mapToDouble(Double::doubleValue).min().orElse(0.0);
        double max = values.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
        double mean = values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        
        // 计算标准差
        double variance = values.stream()
                .mapToDouble(v -> Math.pow(v - mean, 2))
                .average()
                .orElse(0.0);
        double stdDev = Math.sqrt(variance);
        
        // 计算中位数
        List<Double> sortedValues = values.stream().sorted().toList();
        double median;
        int size = sortedValues.size();
        if (size % 2 == 0) {
            median = (sortedValues.get(size / 2 - 1) + sortedValues.get(size / 2)) / 2.0;
        } else {
            median = sortedValues.get(size / 2);
        }
        
        // 检测异常值
        int outlierCount = 0;
        if (stdDev > 0) {
            for (Double value : values) {
                if (Math.abs(value - mean) > outlierThreshold * stdDev) {
                    outlierCount++;
                }
            }
        }
        
        return NormalizationStatistics.builder()
                .min(min)
                .max(max)
                .mean(mean)
                .stdDev(stdDev)
                .median(median)
                .count(values.size())
                .hasOutliers(outlierCount > 0)
                .outlierCount(outlierCount)
                .build();
    }
    
    /**
     * 从数据列表计算统计信息（使用默认异常值阈值3.0）
     * @param values 数据列表
     * @return 统计信息
     */
    public static NormalizationStatistics fromValues(List<Double> values) {
        return fromValues(values, 3.0);
    }
    
    /**
     * 获取数据范围
     * @return max - min
     */
    public double getRange() {
        return max - min;
    }
    
    /**
     * 判断数据是否有效（范围大于0）
     * @return true if 数据有效
     */
    public boolean isValid() {
        return count > 0 && getRange() > 0;
    }
    
    /**
     * 判断是否为常数数据（所有值相同）
     * @return true if 所有值相同
     */
    public boolean isConstant() {
        return getRange() == 0.0;
    }
    
    /**
     * 获取异常值比例
     * @return 异常值比例 [0.0, 1.0]
     */
    public double getOutlierRatio() {
        return count > 0 ? (double) outlierCount / count : 0.0;
    }
}
