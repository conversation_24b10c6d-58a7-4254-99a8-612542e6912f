package com.ctrip.dcs.domain.schedule.sort.hierarchical.enums;

/**
 * 排序大类枚举
 * 定义司机排序的四大类别，每个类别包含多个子项特征
 * 
 * <AUTHOR> Assistant
 */
public enum SortCategory {
    
    /**
     * 时空效率类 - 主要关注空驶时长、空驶距离、空驶成本等时空效率指标
     * 权重建议：40%
     * 包含特征：F9(局部时间间隔)、F10(局部空驶距离)、F4(接驾时间成本)、F5(接驾距离成本)等
     */
    TIME_SPACE_EFFICIENCY("TIME_SPACE_EFFICIENCY", "时空效率类", 0.40),
    
    /**
     * 服务质量类 - 主要关注司机分、司机分总体排名、司机分层等服务质量指标
     * 权重建议：30%
     * 包含特征：F2(司机分占比)、F19(司机分层)、F1(司机分)等
     */
    SERVICE_QUALITY("SERVICE_QUALITY", "服务质量类", 0.30),
    
    /**
     * 订单匹配度类 - 主要关注车型、顺路性、司机意愿车型等订单匹配度指标
     * 权重建议：20%
     * 包含特征：F14(订单里程价值)、车型匹配度、顺路性等
     */
    ORDER_MATCHING("ORDER_MATCHING", "订单匹配度类", 0.20),
    
    /**
     * 全局效率类 - 主要关注司机之间收益均衡、区域调度、平高峰策略等全局效率指标
     * 权重建议：10%
     * 包含特征：F11(未来接单能力)、F13(司机日收益)、收益均衡等
     */
    GLOBAL_EFFICIENCY("GLOBAL_EFFICIENCY", "全局效率类", 0.10);
    
    private final String code;
    private final String description;
    private final double defaultWeight;
    
    SortCategory(String code, String description, double defaultWeight) {
        this.code = code;
        this.description = description;
        this.defaultWeight = defaultWeight;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public double getDefaultWeight() {
        return defaultWeight;
    }
    
    /**
     * 根据代码获取排序大类
     * @param code 大类代码
     * @return 排序大类枚举
     */
    public static SortCategory fromCode(String code) {
        for (SortCategory category : values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        throw new IllegalArgumentException("Unknown sort category code: " + code);
    }
}
