package com.ctrip.dcs.domain.schedule.sort.hierarchical.enums;

/**
 * 聚合策略枚举
 * 定义如何将多个大类的等级聚合为最终的总体等级
 * 
 * <AUTHOR> Assistant
 */
public enum AggregationStrategy {
    
    /**
     * 加权平均GPA策略
     * 根据各大类的权重计算加权平均GPA，然后映射回等级
     * 公式：GPA = Σ(等级分i × 权重i)
     * 适用：平衡各个维度的表现
     */
    WEIGHTED_AVERAGE_GPA("WEIGHTED_AVERAGE_GPA", "加权平均GPA", "根据权重计算平均GPA"),
    
    /**
     * 短板效应策略（最低等级）
     * 总体等级取所有大类中的最低等级
     * 适用：强调底线保障，任何一个维度差都会影响总体
     */
    MINIMUM_GRADE("MINIMUM_GRADE", "短板效应", "取最低等级作为总体等级"),
    
    /**
     * 优势突出策略（最高等级）
     * 总体等级取所有大类中的最高等级
     * 适用：强调优势特长，突出司机的强项
     */
    MAXIMUM_GRADE("MAXIMUM_GRADE", "优势突出", "取最高等级作为总体等级"),
    
    /**
     * 混合策略
     * 结合加权平均和短板效应
     * 如果有D级则直接降级，否则按加权平均计算
     * 适用：既保证底线又兼顾平衡
     */
    HYBRID_STRATEGY("HYBRID_STRATEGY", "混合策略", "结合短板效应和加权平均"),
    
    /**
     * 多数决策略
     * 总体等级取出现次数最多的等级
     * 如果平局则取较高等级
     * 适用：民主决策，避免极端值影响
     */
    MAJORITY_VOTE("MAJORITY_VOTE", "多数决", "取出现最多的等级");
    
    private final String code;
    private final String name;
    private final String description;
    
    AggregationStrategy(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取聚合策略
     * @param code 策略代码
     * @return 聚合策略枚举
     */
    public static AggregationStrategy fromCode(String code) {
        for (AggregationStrategy strategy : values()) {
            if (strategy.getCode().equals(code)) {
                return strategy;
            }
        }
        throw new IllegalArgumentException("Unknown aggregation strategy code: " + code);
    }
}
