package com.ctrip.dcs.domain.schedule.sort.hierarchical.grade;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.AggregationStrategy;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortGrade;
import lombok.Builder;
import lombok.Getter;

import java.util.List;
import java.util.Map;

/**
 * 分层排序分数类
 * 存储司机的完整分层排序结果
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Builder
public class HierarchicalScore {
    
    /**
     * 司机ID
     */
    private Long driverId;
    
    /**
     * 各大类分数
     * key: SortCategory
     * value: CategoryScore
     */
    private Map<SortCategory, CategoryScore> categoryScores;
    
    /**
     * 总体等级
     */
    private SortGrade overallGrade;
    
    /**
     * 总体GPA
     */
    private double overallGpa;
    
    /**
     * 最终排序分数
     * 结合等级和细分分数的最终分数
     */
    private double finalScore;
    
    /**
     * 聚合策略
     */
    private AggregationStrategy aggregationStrategy;
    
    /**
     * 是否启用细分打分
     */
    @Builder.Default
    private boolean fineGrainedScoring = true;
    
    /**
     * 细分分数（用于同等级内排序）
     */
    private double fineGrainedScore;
    
    /**
     * 是否计算成功
     */
    @Builder.Default
    private boolean successful = true;
    
    /**
     * 错误信息列表
     */
    private List<String> errorMessages;
    
    /**
     * 计算时间戳
     */
    @Builder.Default
    private long timestamp = System.currentTimeMillis();
    
    /**
     * 计算耗时（毫秒）
     */
    private long computationTime;
    
    /**
     * 是否为降级结果
     */
    @Builder.Default
    private boolean fallback = false;
    
    /**
     * 创建失败的分层分数
     * @param driverId 司机ID
     * @param errorMessages 错误信息
     * @return 失败的分层分数
     */
    public static HierarchicalScore createFailed(Long driverId, List<String> errorMessages) {
        return HierarchicalScore.builder()
                .driverId(driverId)
                .overallGrade(SortGrade.D)
                .overallGpa(SortGrade.D.getGpaValue())
                .finalScore(0.0)
                .fineGrainedScore(0.0)
                .successful(false)
                .errorMessages(errorMessages)
                .build();
    }
    
    /**
     * 创建降级的分层分数
     * @param driverId 司机ID
     * @param fallbackScore 降级分数
     * @return 降级的分层分数
     */
    public static HierarchicalScore createFallback(Long driverId, double fallbackScore) {
        return HierarchicalScore.builder()
                .driverId(driverId)
                .overallGrade(SortGrade.C) // 降级默认为C级
                .overallGpa(SortGrade.C.getGpaValue())
                .finalScore(fallbackScore)
                .fineGrainedScore(fallbackScore)
                .successful(true)
                .fallback(true)
                .build();
    }
    
    /**
     * 获取指定大类的分数
     * @param category 大类
     * @return 大类分数，如果不存在返回null
     */
    public CategoryScore getCategoryScore(SortCategory category) {
        return categoryScores != null ? categoryScores.get(category) : null;
    }
    
    /**
     * 获取指定大类的等级
     * @param category 大类
     * @return 等级，如果不存在返回D级
     */
    public SortGrade getCategoryGrade(SortCategory category) {
        CategoryScore score = getCategoryScore(category);
        return score != null ? score.getGrade() : SortGrade.D;
    }
    
    /**
     * 获取指定大类的原始分数
     * @param category 大类
     * @return 原始分数，如果不存在返回0.0
     */
    public double getCategoryRawScore(SortCategory category) {
        CategoryScore score = getCategoryScore(category);
        return score != null ? score.getRawScore() : 0.0;
    }
    
    /**
     * 判断是否有D级大类
     * @return true if 存在D级大类
     */
    public boolean hasPoorCategory() {
        if (categoryScores == null) {
            return false;
        }
        return categoryScores.values().stream().anyMatch(CategoryScore::isPoor);
    }
    
    /**
     * 判断是否所有大类都是A级
     * @return true if 所有大类都是A级
     */
    public boolean isAllExcellent() {
        if (categoryScores == null || categoryScores.isEmpty()) {
            return false;
        }
        return categoryScores.values().stream().allMatch(CategoryScore::isExcellent);
    }
    
    /**
     * 获取成功计算的大类数量
     * @return 成功计算的大类数量
     */
    public int getSuccessfulCategoryCount() {
        if (categoryScores == null) {
            return 0;
        }
        return (int) categoryScores.values().stream().filter(CategoryScore::isSuccessful).count();
    }
    
    /**
     * 获取总的大类数量
     * @return 总大类数量
     */
    public int getTotalCategoryCount() {
        return categoryScores != null ? categoryScores.size() : 0;
    }
    
    /**
     * 获取计算成功率
     * @return 成功率 [0.0, 1.0]
     */
    public double getSuccessRate() {
        int total = getTotalCategoryCount();
        return total > 0 ? (double) getSuccessfulCategoryCount() / total : 0.0;
    }
    
    /**
     * 获取分数描述
     * @return 描述字符串
     */
    public String getScoreDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append(String.format("司机%d: %s级 (GPA:%.2f, 最终分数:%.2f)", 
                driverId, overallGrade.getCode(), overallGpa, finalScore));
        
        if (fallback) {
            sb.append(" [降级]");
        }
        
        if (!successful) {
            sb.append(" [失败]");
        }
        
        return sb.toString();
    }
    
    /**
     * 获取详细分数信息
     * @return 详细描述
     */
    public String getDetailedDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append(getScoreDescription()).append("\n");
        
        if (categoryScores != null) {
            categoryScores.forEach((category, score) -> {
                sb.append("  ").append(score.getDescription()).append("\n");
            });
        }
        
        if (errorMessages != null && !errorMessages.isEmpty()) {
            sb.append("错误信息:\n");
            errorMessages.forEach(msg -> sb.append("  - ").append(msg).append("\n"));
        }
        
        return sb.toString();
    }
}
