{"configId": "production_hierarchical_sort_v1", "configName": "生产环境分层排序配置V1", "description": "基于四大类的分层排序配置，适用于生产环境", "enabled": true, "enableFineGrainedScoring": true, "monitoringEnabled": true, "fallbackThreshold": 0.05, "aggregationStrategy": "WEIGHTED_AVERAGE_GPA", "categoryWeights": {"TIME_SPACE_EFFICIENCY": 0.4, "SERVICE_QUALITY": 0.3, "ORDER_MATCHING": 0.2, "GLOBAL_EFFICIENCY": 0.1}, "citySpecificWeights": {"1": {"TIME_SPACE_EFFICIENCY": 0.45, "SERVICE_QUALITY": 0.25, "ORDER_MATCHING": 0.2, "GLOBAL_EFFICIENCY": 0.1}, "2": {"TIME_SPACE_EFFICIENCY": 0.35, "SERVICE_QUALITY": 0.35, "ORDER_MATCHING": 0.2, "GLOBAL_EFFICIENCY": 0.1}}, "timeSpecificWeights": {"PEAK": {"TIME_SPACE_EFFICIENCY": 0.5, "SERVICE_QUALITY": 0.25, "ORDER_MATCHING": 0.15, "GLOBAL_EFFICIENCY": 0.1}, "OFF_PEAK": {"TIME_SPACE_EFFICIENCY": 0.35, "SERVICE_QUALITY": 0.3, "ORDER_MATCHING": 0.25, "GLOBAL_EFFICIENCY": 0.1}, "NIGHT": {"TIME_SPACE_EFFICIENCY": 0.3, "SERVICE_QUALITY": 0.4, "ORDER_MATCHING": 0.2, "GLOBAL_EFFICIENCY": 0.1}}, "featureConfigs": {"F2": {"featureId": "F2", "featureName": "司机分占比", "category": "SERVICE_QUALITY", "weight": 0.5, "normalizationType": "MIN_MAX", "enabled": true, "description": "司机评分在同城市司机中的占比，反映服务质量"}, "F1": {"featureId": "F1", "featureName": "司机分", "category": "SERVICE_QUALITY", "weight": 0.3, "normalizationType": "MIN_MAX", "enabled": true, "description": "司机的绝对评分"}, "F19": {"featureId": "F19", "featureName": "司机分层", "category": "SERVICE_QUALITY", "weight": 0.2, "normalizationType": "NONE", "enabled": true, "description": "司机分层等级，直接使用原值"}, "F9": {"featureId": "F9", "featureName": "局部时间间隔", "category": "TIME_SPACE_EFFICIENCY", "weight": 0.4, "normalizationType": "MIN_MAX", "enabled": true, "description": "司机到达上车点的时间间隔"}, "F10": {"featureId": "F10", "featureName": "局部空驶距离", "category": "TIME_SPACE_EFFICIENCY", "weight": 0.4, "normalizationType": "INVERSE_MIN_MAX", "enabled": true, "description": "司机到达上车点的空驶距离，距离越短越好"}, "F4": {"featureId": "F4", "featureName": "接驾时间成本", "category": "TIME_SPACE_EFFICIENCY", "weight": 0.1, "normalizationType": "INVERSE_MIN_MAX", "enabled": true, "description": "接驾时间成本，时间越短越好"}, "F5": {"featureId": "F5", "featureName": "接驾距离成本", "category": "TIME_SPACE_EFFICIENCY", "weight": 0.1, "normalizationType": "INVERSE_MIN_MAX", "enabled": true, "description": "接驾距离成本，距离越短越好"}, "F14": {"featureId": "F14", "featureName": "订单里程价值", "category": "ORDER_MATCHING", "weight": 1.0, "normalizationType": "MIN_MAX", "enabled": true, "description": "订单的里程价值，反映订单与司机的匹配度"}, "F11": {"featureId": "F11", "featureName": "未来接单能力", "category": "GLOBAL_EFFICIENCY", "weight": 0.4, "normalizationType": "MIN_MAX", "enabled": true, "description": "司机完成当前订单后的接单能力"}, "F13": {"featureId": "F13", "featureName": "司机日收益", "category": "GLOBAL_EFFICIENCY", "weight": 0.6, "normalizationType": "LOG_NORMALIZATION", "enabled": true, "description": "司机当日收益，使用对数归一化处理长尾分布"}}, "gradeThresholds": {"A": 85.0, "B": 70.0, "C": 50.0}, "normalizationConfig": {"logBase": 10, "sigmoidSteepness": 1.0, "outlierThreshold": 3.0, "minMaxClipping": true}, "monitoringConfig": {"enableDetailedLogging": true, "sampleRate": 0.1, "performanceThreshold": 1000, "successRateThreshold": 0.95}}