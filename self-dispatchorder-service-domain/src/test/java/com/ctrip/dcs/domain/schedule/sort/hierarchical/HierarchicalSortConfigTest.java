package com.ctrip.dcs.domain.schedule.sort.hierarchical;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.FeatureConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.HierarchicalSortConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.AggregationStrategy;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.NormalizationType;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 分层排序配置测试
 * 
 * <AUTHOR> Assistant
 */
public class HierarchicalSortConfigTest {
    
    @Test
    public void testValidConfig() {
        // 创建有效配置
        HierarchicalSortConfig config = createValidConfig();
        
        // 验证配置有效性
        assertTrue(config.isValid());
        assertTrue(config.isEnabled());
        assertEquals("test_config", config.getConfigId());
        assertEquals(AggregationStrategy.WEIGHTED_AVERAGE_GPA, config.getAggregationStrategy());
    }
    
    @Test
    public void testInvalidConfig() {
        // 测试空配置ID
        HierarchicalSortConfig config1 = HierarchicalSortConfig.builder()
                .configId("")
                .enabled(true)
                .build();
        assertFalse(config1.isValid());
        
        // 测试权重和不为1
        Map<SortCategory, Double> invalidWeights = Map.of(
                SortCategory.TIME_SPACE_EFFICIENCY, 0.5,
                SortCategory.SERVICE_QUALITY, 0.3
        );
        HierarchicalSortConfig config2 = HierarchicalSortConfig.builder()
                .configId("test")
                .enabled(true)
                .categoryWeights(invalidWeights)
                .build();
        assertFalse(config2.isValid());
    }
    
    @Test
    public void testCategoryWeights() {
        HierarchicalSortConfig config = createValidConfig();
        
        // 测试默认权重
        Map<SortCategory, Double> defaultWeights = config.getCategoryWeights(null, null);
        assertEquals(4, defaultWeights.size());
        assertEquals(0.4, defaultWeights.get(SortCategory.TIME_SPACE_EFFICIENCY), 0.001);
        
        // 测试城市特定权重
        Map<SortCategory, Double> cityWeights = config.getCategoryWeights(1, null);
        assertNotNull(cityWeights);
        
        // 测试时段特定权重
        Map<SortCategory, Double> timeWeights = config.getCategoryWeights(null, "PEAK");
        assertNotNull(timeWeights);
    }
    
    @Test
    public void testFeatureConfigs() {
        HierarchicalSortConfig config = createValidConfig();
        
        // 验证特征项配置
        Map<String, FeatureConfig> featureConfigs = config.getFeatureConfigs();
        assertNotNull(featureConfigs);
        assertTrue(featureConfigs.containsKey("F2"));
        
        FeatureConfig f2Config = featureConfigs.get("F2");
        assertEquals("F2", f2Config.getFeatureId());
        assertEquals(SortCategory.SERVICE_QUALITY, f2Config.getCategory());
        assertTrue(f2Config.isEnabled());
    }
    
    @Test
    public void testGradeThresholds() {
        HierarchicalSortConfig config = createValidConfig();
        
        // 验证等级阈值
        Map<String, Double> thresholds = config.getGradeThresholds();
        assertNotNull(thresholds);
        assertEquals(85.0, thresholds.get("A"), 0.001);
        assertEquals(70.0, thresholds.get("B"), 0.001);
        assertEquals(50.0, thresholds.get("C"), 0.001);
    }
    
    @Test
    public void testConfigBuilder() {
        // 测试建造者模式
        HierarchicalSortConfig config = HierarchicalSortConfig.builder()
                .configId("builder_test")
                .configName("Builder Test Config")
                .description("Test config created with builder")
                .enabled(true)
                .enableFineGrainedScoring(true)
                .monitoringEnabled(true)
                .fallbackThreshold(0.1)
                .aggregationStrategy(AggregationStrategy.HYBRID_STRATEGY)
                .build();
        
        assertEquals("builder_test", config.getConfigId());
        assertEquals("Builder Test Config", config.getConfigName());
        assertTrue(config.isEnableFineGrainedScoring());
        assertTrue(config.isMonitoringEnabled());
        assertEquals(0.1, config.getFallbackThreshold(), 0.001);
        assertEquals(AggregationStrategy.HYBRID_STRATEGY, config.getAggregationStrategy());
    }
    
    @Test
    public void testConfigCopy() {
        HierarchicalSortConfig original = createValidConfig();
        
        // 创建配置副本
        HierarchicalSortConfig copy = original.toBuilder()
                .configId("copy_config")
                .enabled(false)
                .build();
        
        assertEquals("copy_config", copy.getConfigId());
        assertFalse(copy.isEnabled());
        assertEquals(original.getConfigName(), copy.getConfigName());
        assertEquals(original.getCategoryWeights(null, null), copy.getCategoryWeights(null, null));
    }
    
    @Test
    public void testFeatureConfigValidation() {
        // 测试特征项配置验证
        FeatureConfig validConfig = FeatureConfig.builder()
                .featureId("TEST_FEATURE")
                .featureName("Test Feature")
                .category(SortCategory.TIME_SPACE_EFFICIENCY)
                .weight(0.5)
                .normalizationType(NormalizationType.MIN_MAX)
                .enabled(true)
                .build();
        
        assertTrue(validConfig.isValid());
        
        // 测试无效配置
        FeatureConfig invalidConfig = FeatureConfig.builder()
                .featureId("")
                .weight(-0.1)
                .build();
        
        assertFalse(invalidConfig.isValid());
    }
    
    /**
     * 创建有效的测试配置
     */
    private HierarchicalSortConfig createValidConfig() {
        // 创建大类权重
        Map<SortCategory, Double> categoryWeights = Map.of(
                SortCategory.TIME_SPACE_EFFICIENCY, 0.4,
                SortCategory.SERVICE_QUALITY, 0.3,
                SortCategory.ORDER_MATCHING, 0.2,
                SortCategory.GLOBAL_EFFICIENCY, 0.1
        );
        
        // 创建特征项配置
        Map<String, FeatureConfig> featureConfigs = new HashMap<>();
        featureConfigs.put("F2", FeatureConfig.builder()
                .featureId("F2")
                .featureName("司机分占比")
                .category(SortCategory.SERVICE_QUALITY)
                .weight(1.0)
                .normalizationType(NormalizationType.MIN_MAX)
                .enabled(true)
                .build());
        
        featureConfigs.put("F9", FeatureConfig.builder()
                .featureId("F9")
                .featureName("局部时间间隔")
                .category(SortCategory.TIME_SPACE_EFFICIENCY)
                .weight(0.8)
                .normalizationType(NormalizationType.MIN_MAX)
                .enabled(true)
                .build());
        
        // 创建等级阈值
        Map<String, Double> gradeThresholds = Map.of(
                "A", 85.0,
                "B", 70.0,
                "C", 50.0
        );
        
        return HierarchicalSortConfig.builder()
                .configId("test_config")
                .configName("Test Hierarchical Sort Config")
                .description("Test configuration for hierarchical sorting")
                .enabled(true)
                .categoryWeights(categoryWeights)
                .featureConfigs(featureConfigs)
                .gradeThresholds(gradeThresholds)
                .aggregationStrategy(AggregationStrategy.WEIGHTED_AVERAGE_GPA)
                .enableFineGrainedScoring(false)
                .monitoringEnabled(true)
                .fallbackThreshold(0.05)
                .build();
    }
}
