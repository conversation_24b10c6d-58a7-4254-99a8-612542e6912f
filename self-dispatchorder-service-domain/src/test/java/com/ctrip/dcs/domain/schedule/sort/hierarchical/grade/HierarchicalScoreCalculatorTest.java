package com.ctrip.dcs.domain.schedule.sort.hierarchical.grade;

import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.feature.Value;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.FeatureConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.HierarchicalSortConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.AggregationStrategy;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.NormalizationType;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortGrade;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 分层排序分数计算器测试
 * 
 * <AUTHOR> Assistant
 */
public class HierarchicalScoreCalculatorTest {
    
    @Mock
    private SortContext mockContext;

    @Mock
    private DspOrderVO mockOrder;
    
    private HierarchicalScoreCalculator calculator;
    private HierarchicalSortConfig config;
    
    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 创建测试配置
        config = createTestConfig();
        calculator = new HierarchicalScoreCalculator(config);
        
        // 设置Mock对象
        when(mockContext.getDspOrder()).thenReturn(mockOrder);
        when(mockOrder.getCityId()).thenReturn(1);
        when(mockOrder.getEstimatedUseTime()).thenReturn(new Date());
    }
    
    @Test
    public void testCalculateScoreSuccess() {
        // 创建测试司机模型
        SortModel model = createTestSortModel(1001L);
        
        // 添加特征值
        model.addValue(new Value("F2", 85.0, Map.of()));  // 司机分占比 - A级
        model.addValue(new Value("F9", 75.0, Map.of()));  // 局部时间间隔 - B级
        
        // 计算分数
        HierarchicalScore score = calculator.calculateScore(model, mockContext);
        
        // 验证结果
        assertNotNull(score);
        assertTrue(score.isSuccessful());
        assertEquals(1001L, score.getDriverId());
        assertNotNull(score.getOverallGrade());
        assertTrue(score.getOverallGpa() > 0);
        assertTrue(score.getFinalScore() > 0);
        
        // 验证大类分数
        assertEquals(2, score.getCategoryScores().size());
        assertTrue(score.getCategoryScores().containsKey(SortCategory.SERVICE_QUALITY));
        assertTrue(score.getCategoryScores().containsKey(SortCategory.TIME_SPACE_EFFICIENCY));
    }
    
    @Test
    public void testCalculateScoreWithDifferentGrades() {
        SortModel model = createTestSortModel(1002L);
        
        // 添加不同等级的特征值
        model.addValue(new Value("F2", 95.0, Map.of()));  // A级
        model.addValue(new Value("F9", 45.0, Map.of()));  // D级
        
        HierarchicalScore score = calculator.calculateScore(model, mockContext);
        
        assertTrue(score.isSuccessful());
        
        // 验证大类等级
        CategoryScore serviceQuality = score.getCategoryScore(SortCategory.SERVICE_QUALITY);
        CategoryScore timeSpaceEfficiency = score.getCategoryScore(SortCategory.TIME_SPACE_EFFICIENCY);
        
        assertEquals(SortGrade.A, serviceQuality.getGrade());
        assertEquals(SortGrade.D, timeSpaceEfficiency.getGrade());
    }
    
    @Test
    public void testAggregationStrategies() {
        SortModel model = createTestSortModel(1003L);
        model.addValue(new Value("F2", 85.0, Map.of()));  // A级
        model.addValue(new Value("F9", 75.0, Map.of()));  // B级
        
        // 测试加权平均策略
        config = config.toBuilder()
                .aggregationStrategy(AggregationStrategy.WEIGHTED_AVERAGE_GPA)
                .build();
        calculator = new HierarchicalScoreCalculator(config);
        
        HierarchicalScore weightedScore = calculator.calculateScore(model, mockContext);
        assertTrue(weightedScore.isSuccessful());
        
        // 测试最小值策略
        config = config.toBuilder()
                .aggregationStrategy(AggregationStrategy.MINIMUM_GRADE)
                .build();
        calculator = new HierarchicalScoreCalculator(config);
        
        HierarchicalScore minScore = calculator.calculateScore(model, mockContext);
        assertTrue(minScore.isSuccessful());
        assertEquals(SortGrade.B, minScore.getOverallGrade()); // 应该是最低的B级
    }
    
    @Test
    public void testBatchCalculation() {
        List<SortModel> models = Arrays.asList(
                createTestSortModelWithValues(1001L, 85.0, 75.0),
                createTestSortModelWithValues(1002L, 95.0, 85.0),
                createTestSortModelWithValues(1003L, 65.0, 55.0)
        );
        
        List<HierarchicalScore> scores = calculator.calculateScores(models, mockContext);
        
        assertEquals(3, scores.size());
        assertTrue(scores.stream().allMatch(HierarchicalScore::isSuccessful));
        
        // 验证分数排序（分数高的应该排在前面）
        HierarchicalScore score1 = scores.get(0);
        HierarchicalScore score2 = scores.get(1);
        HierarchicalScore score3 = scores.get(2);
        
        assertEquals(1001L, score1.getDriverId());
        assertEquals(1002L, score2.getDriverId());
        assertEquals(1003L, score3.getDriverId());
    }
    
    @Test
    public void testErrorHandling() {
        // 创建没有特征值的模型
        SortModel emptyModel = createTestSortModel(1004L);
        
        HierarchicalScore score = calculator.calculateScore(emptyModel, mockContext);
        
        // 应该能处理空特征值的情况
        assertNotNull(score);
        assertEquals(1004L, score.getDriverId());
    }
    
    @Test
    public void testFineGrainedScoring() {
        // 启用细分分数
        config = config.toBuilder()
                .enableFineGrainedScoring(true)
                .build();
        calculator = new HierarchicalScoreCalculator(config);
        
        SortModel model = createTestSortModelWithValues(1005L, 85.0, 75.0);
        HierarchicalScore score = calculator.calculateScore(model, mockContext);
        
        assertTrue(score.isSuccessful());
        assertTrue(score.isFineGrainedScoring());
        assertTrue(score.getFineGrainedScore() > 0);
    }
    
    @Test
    public void testCategoryWeightCalculation() {
        SortModel model = createTestSortModelWithValues(1006L, 80.0, 80.0);
        HierarchicalScore score = calculator.calculateScore(model, mockContext);
        
        assertTrue(score.isSuccessful());
        
        // 验证权重计算
        CategoryScore serviceQuality = score.getCategoryScore(SortCategory.SERVICE_QUALITY);
        CategoryScore timeSpaceEfficiency = score.getCategoryScore(SortCategory.TIME_SPACE_EFFICIENCY);
        
        assertEquals(0.3, serviceQuality.getWeight(), 0.001);
        assertEquals(0.4, timeSpaceEfficiency.getWeight(), 0.001);
        
        // 验证加权分数
        double expectedWeightedScore1 = serviceQuality.getRawScore() * 0.3;
        double expectedWeightedScore2 = timeSpaceEfficiency.getRawScore() * 0.4;
        
        assertEquals(expectedWeightedScore1, serviceQuality.getWeightedScore(), 0.001);
        assertEquals(expectedWeightedScore2, timeSpaceEfficiency.getWeightedScore(), 0.001);
    }
    
    @Test
    public void testGradeMapping() {
        // 测试不同分数的等级映射
        SortModel highScoreModel = createTestSortModelWithValues(1007L, 95.0, 90.0);
        SortModel mediumScoreModel = createTestSortModelWithValues(1008L, 75.0, 70.0);
        SortModel lowScoreModel = createTestSortModelWithValues(1009L, 45.0, 40.0);
        
        HierarchicalScore highScore = calculator.calculateScore(highScoreModel, mockContext);
        HierarchicalScore mediumScore = calculator.calculateScore(mediumScoreModel, mockContext);
        HierarchicalScore lowScore = calculator.calculateScore(lowScoreModel, mockContext);
        
        // 验证等级映射
        assertTrue(highScore.getOverallGrade().getGpaValue() > mediumScore.getOverallGrade().getGpaValue());
        assertTrue(mediumScore.getOverallGrade().getGpaValue() > lowScore.getOverallGrade().getGpaValue());
    }
    
    /**
     * 创建测试配置
     */
    private HierarchicalSortConfig createTestConfig() {
        Map<SortCategory, Double> categoryWeights = Map.of(
                SortCategory.TIME_SPACE_EFFICIENCY, 0.4,
                SortCategory.SERVICE_QUALITY, 0.3,
                SortCategory.ORDER_MATCHING, 0.2,
                SortCategory.GLOBAL_EFFICIENCY, 0.1
        );
        
        Map<String, FeatureConfig> featureConfigs = Map.of(
                "F2", FeatureConfig.builder()
                        .featureId("F2")
                        .category(SortCategory.SERVICE_QUALITY)
                        .weight(1.0)
                        .normalizationType(NormalizationType.MIN_MAX)
                        .enabled(true)
                        .build(),
                "F9", FeatureConfig.builder()
                        .featureId("F9")
                        .category(SortCategory.TIME_SPACE_EFFICIENCY)
                        .weight(1.0)
                        .normalizationType(NormalizationType.MIN_MAX)
                        .enabled(true)
                        .build()
        );
        
        return HierarchicalSortConfig.builder()
                .configId("test_config")
                .enabled(true)
                .categoryWeights(categoryWeights)
                .featureConfigs(featureConfigs)
                .aggregationStrategy(AggregationStrategy.WEIGHTED_AVERAGE_GPA)
                .enableFineGrainedScoring(false)
                .monitoringEnabled(true)
                .fallbackThreshold(0.05)
                .build();
    }
    
    /**
     * 创建测试司机模型
     */
    private SortModel createTestSortModel(Long driverId) {
        DriverVO driver = DriverVO.builder()
                .driverId(driverId)
                .driverName("测试司机" + driverId)
                .build();

        DspModelVO dspModel = new DspModelVO(mockOrder, driver);

        return new SortModel(dspModel);
    }
    
    /**
     * 创建带特征值的测试司机模型
     */
    private SortModel createTestSortModelWithValues(Long driverId, double f2Value, double f9Value) {
        SortModel model = createTestSortModel(driverId);
        model.addValue(new Value("F2", f2Value, Map.of()));
        model.addValue(new Value("F9", f9Value, Map.of()));
        return model;
    }
}
