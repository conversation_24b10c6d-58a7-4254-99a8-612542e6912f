package com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.FeatureConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.NormalizationType;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.normalization.impl.*;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 归一化算法测试
 * 
 * <AUTHOR> Assistant
 */
public class NormalizationTest {
    
    @Test
    public void testMinMaxNormalizer() {
        MinMaxNormalizer normalizer = new MinMaxNormalizer();
        List<Double> values = Arrays.asList(1.0, 2.0, 3.0, 4.0, 5.0);
        NormalizationStatistics stats = NormalizationStatistics.fromValues(values);
        FeatureConfig config = createTestConfig();
        
        // 测试边界值
        assertEquals(0.0, normalizer.normalize(1.0, stats, config), 0.001);
        assertEquals(1.0, normalizer.normalize(5.0, stats, config), 0.001);
        
        // 测试中间值
        assertEquals(0.5, normalizer.normalize(3.0, stats, config), 0.001);
        
        // 测试超出范围的值
        assertEquals(0.0, normalizer.normalize(0.0, stats, config), 0.001);
        assertEquals(1.0, normalizer.normalize(6.0, stats, config), 0.001);
    }
    
    @Test
    public void testInverseMinMaxNormalizer() {
        InverseMinMaxNormalizer normalizer = new InverseMinMaxNormalizer();
        List<Double> values = Arrays.asList(1.0, 2.0, 3.0, 4.0, 5.0);
        NormalizationStatistics stats = NormalizationStatistics.fromValues(values);
        FeatureConfig config = createTestConfig();
        
        // 测试反向归一化
        assertEquals(1.0, normalizer.normalize(1.0, stats, config), 0.001);
        assertEquals(0.0, normalizer.normalize(5.0, stats, config), 0.001);
        assertEquals(0.5, normalizer.normalize(3.0, stats, config), 0.001);
    }
    
    @Test
    public void testZScoreNormalizer() {
        ZScoreNormalizer normalizer = new ZScoreNormalizer();
        List<Double> values = Arrays.asList(1.0, 2.0, 3.0, 4.0, 5.0);
        NormalizationStatistics stats = NormalizationStatistics.fromValues(values);
        FeatureConfig config = createTestConfig();
        
        // 测试均值归一化为0.5
        double normalizedMean = normalizer.normalize(stats.getMean(), stats, config);
        assertEquals(0.5, normalizedMean, 0.1);
        
        // 测试归一化结果在[0,1]范围内
        for (double value : values) {
            double normalized = normalizer.normalize(value, stats, config);
            assertTrue(normalized >= 0.0 && normalized <= 1.0);
        }
    }
    
    @Test
    public void testLogNormalizer() {
        LogNormalizer normalizer = new LogNormalizer();
        List<Double> values = Arrays.asList(1.0, 10.0, 100.0, 1000.0, 10000.0);
        NormalizationStatistics stats = NormalizationStatistics.fromValues(values);
        FeatureConfig config = createTestConfig();
        
        // 测试对数归一化
        double normalized1 = normalizer.normalize(1.0, stats, config);
        double normalized10 = normalizer.normalize(10.0, stats, config);
        double normalized100 = normalizer.normalize(100.0, stats, config);
        
        // 对数归一化应该压缩大值的差异
        assertTrue(normalized10 - normalized1 > normalized100 - normalized10);
        
        // 测试结果在[0,1]范围内
        assertTrue(normalized1 >= 0.0 && normalized1 <= 1.0);
        assertTrue(normalized100 >= 0.0 && normalized100 <= 1.0);
    }
    
    @Test
    public void testSigmoidNormalizer() {
        SigmoidNormalizer normalizer = new SigmoidNormalizer();
        List<Double> values = Arrays.asList(-5.0, -2.0, 0.0, 2.0, 5.0);
        NormalizationStatistics stats = NormalizationStatistics.fromValues(values);
        FeatureConfig config = createTestConfig();
        
        // 测试Sigmoid归一化
        double normalized0 = normalizer.normalize(0.0, stats, config);
        assertEquals(0.5, normalized0, 0.1); // 0应该映射到0.5附近
        
        // 测试极值
        double normalizedMin = normalizer.normalize(-10.0, stats, config);
        double normalizedMax = normalizer.normalize(10.0, stats, config);
        
        assertTrue(normalizedMin < 0.1);
        assertTrue(normalizedMax > 0.9);
    }
    
    @Test
    public void testRankNormalizer() {
        RankNormalizer normalizer = new RankNormalizer();
        List<Double> values = Arrays.asList(1.0, 3.0, 2.0, 5.0, 4.0);
        NormalizationStatistics stats = NormalizationStatistics.fromValues(values);
        FeatureConfig config = createTestConfig();
        
        // 测试排名归一化
        assertEquals(0.0, normalizer.normalize(1.0, stats, config), 0.001); // 最小值排名第1
        assertEquals(0.25, normalizer.normalize(2.0, stats, config), 0.001); // 第2小
        assertEquals(0.5, normalizer.normalize(3.0, stats, config), 0.001); // 第3小
        assertEquals(0.75, normalizer.normalize(4.0, stats, config), 0.001); // 第4小
        assertEquals(1.0, normalizer.normalize(5.0, stats, config), 0.001); // 最大值排名第5
    }
    
    @Test
    public void testNoNormalizer() {
        NoNormalizer normalizer = new NoNormalizer();
        NormalizationStatistics stats = NormalizationStatistics.builder().build();
        FeatureConfig config = createTestConfig();
        
        // 测试不归一化
        assertEquals(5.0, normalizer.normalize(5.0, stats, config), 0.001);
        assertEquals(-3.0, normalizer.normalize(-3.0, stats, config), 0.001);
        assertEquals(0.0, normalizer.normalize(0.0, stats, config), 0.001);
    }
    
    @Test
    public void testNormalizerFactory() {
        // 测试工厂方法
        assertTrue(NormalizerFactory.getNormalizer(NormalizationType.MIN_MAX) instanceof MinMaxNormalizer);
        assertTrue(NormalizerFactory.getNormalizer(NormalizationType.INVERSE_MIN_MAX) instanceof InverseMinMaxNormalizer);
        assertTrue(NormalizerFactory.getNormalizer(NormalizationType.Z_SCORE) instanceof ZScoreNormalizer);
        assertTrue(NormalizerFactory.getNormalizer(NormalizationType.LOG_NORMALIZATION) instanceof LogNormalizer);
        assertTrue(NormalizerFactory.getNormalizer(NormalizationType.SIGMOID) instanceof SigmoidNormalizer);
        assertTrue(NormalizerFactory.getNormalizer(NormalizationType.RANK_NORMALIZATION) instanceof RankNormalizer);
        assertTrue(NormalizerFactory.getNormalizer(NormalizationType.NONE) instanceof NoNormalizer);
    }
    
    @Test
    public void testNormalizationStatistics() {
        List<Double> values = Arrays.asList(1.0, 2.0, 3.0, 4.0, 5.0);
        NormalizationStatistics stats = NormalizationStatistics.fromValues(values);
        
        assertEquals(1.0, stats.getMin(), 0.001);
        assertEquals(5.0, stats.getMax(), 0.001);
        assertEquals(3.0, stats.getMean(), 0.001);
        assertEquals(Math.sqrt(2.5), stats.getStdDev(), 0.001);
        assertEquals(5, stats.getCount());
    }
    
    @Test
    public void testEdgeCases() {
        // 测试单个值的情况
        List<Double> singleValue = Arrays.asList(5.0);
        NormalizationStatistics singleStats = NormalizationStatistics.fromValues(singleValue);
        FeatureConfig config = createTestConfig();
        
        MinMaxNormalizer minMaxNormalizer = new MinMaxNormalizer();
        assertEquals(0.5, minMaxNormalizer.normalize(5.0, singleStats, config), 0.001);
        
        // 测试空值情况
        NormalizationStatistics emptyStats = NormalizationStatistics.builder()
                .min(0.0)
                .max(0.0)
                .mean(0.0)
                .stdDev(0.0)
                .count(0)
                .build();
        
        assertEquals(0.0, minMaxNormalizer.normalize(5.0, emptyStats, config), 0.001);
    }
    
    @Test
    public void testNormalizationWithOutliers() {
        // 测试包含异常值的数据
        List<Double> valuesWithOutliers = Arrays.asList(1.0, 2.0, 3.0, 4.0, 100.0);
        NormalizationStatistics stats = NormalizationStatistics.fromValues(valuesWithOutliers);
        FeatureConfig config = createTestConfig();
        
        LogNormalizer logNormalizer = new LogNormalizer();
        
        // 对数归一化应该能更好地处理异常值
        double normalized1 = logNormalizer.normalize(1.0, stats, config);
        double normalized4 = logNormalizer.normalize(4.0, stats, config);
        double normalized100 = logNormalizer.normalize(100.0, stats, config);
        
        // 验证对数归一化压缩了异常值的影响
        assertTrue(normalized4 - normalized1 < 1.0 - normalized4);
        assertTrue(normalized100 <= 1.0);
    }
    
    /**
     * 创建测试用的特征配置
     */
    private FeatureConfig createTestConfig() {
        return FeatureConfig.builder()
                .featureId("TEST_FEATURE")
                .featureName("Test Feature")
                .category(SortCategory.TIME_SPACE_EFFICIENCY)
                .weight(1.0)
                .normalizationType(NormalizationType.MIN_MAX)
                .enabled(true)
                .build();
    }
}
