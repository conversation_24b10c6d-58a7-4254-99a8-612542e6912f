# 分层排序系统使用指南

## 概述

分层排序系统是对传统201排序项的重新设计，基于四大类（时空效率、服务质量、订单匹配度、全局效率）的分层架构，采用等级制评分和GPA计算方式，提供更加科学和可配置的司机排序方案。

## 核心概念

### 四大类体系

1. **时空效率 (TIME_SPACE_EFFICIENCY)** - 默认权重40%
   - 接驾时间、距离相关指标
   - 体现就近派单原则

2. **服务质量 (SERVICE_QUALITY)** - 默认权重30%
   - 司机评分、分层等指标
   - 体现服务质量优先

3. **订单匹配度 (ORDER_MATCHING)** - 默认权重20%
   - 订单价值、司机偏好匹配
   - 体现供需匹配效率

4. **全局效率 (GLOBAL_EFFICIENCY)** - 默认权重10%
   - 司机收益、未来接单能力
   - 体现整体运营效率

### 等级制评分

- **A级**: 85-100分，GPA=4.0
- **B级**: 70-85分，GPA=3.0  
- **C级**: 50-70分，GPA=2.0
- **D级**: 0-50分，GPA=1.0

### 聚合策略

- **加权平均GPA**: 按权重计算各大类GPA的加权平均
- **最小等级**: 取各大类中最低等级作为总体等级
- **混合策略**: 有D级则为D级，否则按加权平均

## 快速开始

### 1. 创建配置

```java
// 创建默认配置
HierarchicalSortConfig config = HierarchicalSortFactory.createDefaultConfig();

// 或自定义配置
HierarchicalSortConfig customConfig = HierarchicalSortConfig.builder()
    .configId("custom_config")
    .enabled(true)
    .categoryWeights(Map.of(
        SortCategory.TIME_SPACE_EFFICIENCY, 0.45,
        SortCategory.SERVICE_QUALITY, 0.25,
        SortCategory.ORDER_MATCHING, 0.20,
        SortCategory.GLOBAL_EFFICIENCY, 0.10
    ))
    .build();
```

### 2. 创建排序器

```java
// 获取传统特征项
List<Feature> traditionalFeatures = getTraditionalFeatures();

// 创建分层排序器
HierarchicalSorter sorter = hierarchicalSortFactory.createHierarchicalSorter(config);
```

### 3. 执行排序

```java
// 执行分层排序
List<SortModel> sortedModels = sorter.sort(models, context);

// 检查排序结果
for (SortModel model : sortedModels) {
    Double score = getHierarchicalScore(model);
    System.out.println("司机ID: " + model.getDriverId() + ", 分数: " + score);
}
```

## 配置详解

### 大类权重配置

```java
// 基础权重
Map<SortCategory, Double> baseWeights = Map.of(
    SortCategory.TIME_SPACE_EFFICIENCY, 0.40,
    SortCategory.SERVICE_QUALITY, 0.30,
    SortCategory.ORDER_MATCHING, 0.20,
    SortCategory.GLOBAL_EFFICIENCY, 0.10
);

// 城市特定权重
Map<Integer, Map<SortCategory, Double>> cityWeights = Map.of(
    1, Map.of(SortCategory.TIME_SPACE_EFFICIENCY, 0.45), // 北京加强时空效率
    2, Map.of(SortCategory.SERVICE_QUALITY, 0.35)        // 上海加强服务质量
);

// 时段特定权重  
Map<String, Map<SortCategory, Double>> timeWeights = Map.of(
    "PEAK", Map.of(SortCategory.TIME_SPACE_EFFICIENCY, 0.50), // 高峰期强调效率
    "NIGHT", Map.of(SortCategory.SERVICE_QUALITY, 0.40)       // 夜间强调服务
);
```

### 特征项配置

```java
FeatureConfig f2Config = FeatureConfig.builder()
    .featureId("F2")
    .featureName("司机分占比")
    .category(SortCategory.SERVICE_QUALITY)
    .weight(0.5)
    .normalizationType(NormalizationType.MIN_MAX)
    .enabled(true)
    .build();
```

### 归一化算法选择

- **MIN_MAX**: 线性归一化到[0,1]，适用于正态分布
- **INVERSE_MIN_MAX**: 反向归一化，适用于"越小越好"的指标
- **Z_SCORE**: 标准化后Sigmoid映射，适用于有异常值的数据
- **LOG_NORMALIZATION**: 对数归一化，适用于长尾分布（如收益）
- **SIGMOID**: S型曲线映射，适用于需要平滑过渡的指标
- **RANK_NORMALIZATION**: 排名归一化，适用于序数数据
- **NONE**: 不归一化，直接使用原值

## 监控和运维

### 健康检查

```java
// 检查排序器健康状态
boolean isHealthy = sorter.isHealthy();

// 获取监控报告
String report = sorter.getMonitor().getMonitoringReport();
System.out.println(report);
```

### 性能监控

系统自动收集以下指标：
- 排序成功率
- 平均处理时间
- 降级率
- 各等级分布

### 降级机制

当分层排序失败时，系统会自动降级到传统排序：
- 计算失败率超过阈值
- 处理时间过长
- 系统异常

## 最佳实践

### 1. 权重配置建议

- **高峰期**: 提高时空效率权重至50%
- **夜间**: 提高服务质量权重至40%
- **机场/火车站**: 提高订单匹配度权重
- **新司机较多区域**: 适当降低服务质量权重

### 2. 特征项选择

- 保留核心特征项：F2(司机分)、F9(时间)、F10(距离)
- 根据业务需要添加：F11(接单能力)、F13(收益)、F14(订单价值)
- 定期评估特征项有效性

### 3. 归一化策略

- 距离、时间类指标：使用INVERSE_MIN_MAX
- 评分类指标：使用MIN_MAX
- 收益类指标：使用LOG_NORMALIZATION
- 分层等级：使用NONE

### 4. 监控告警

- 成功率低于95%时告警
- 平均处理时间超过1秒时告警
- 降级率超过5%时告警

## 故障排查

### 常见问题

1. **排序结果异常**
   - 检查特征项配置是否正确
   - 验证权重配置是否合理
   - 查看归一化算法是否适合数据分布

2. **性能问题**
   - 检查特征项数量是否过多
   - 优化归一化算法选择
   - 考虑禁用细分分数计算

3. **降级频繁**
   - 检查特征项计算是否稳定
   - 调整降级阈值
   - 优化异常处理逻辑

### 调试工具

```java
// 启用详细日志
config = config.toBuilder()
    .monitoringEnabled(true)
    .build();

// 分析排序结果
HierarchicalSortAnalyzer analyzer = new HierarchicalSortAnalyzer();
AnalysisResult result = analyzer.analyzeScores(scores);
String report = analyzer.generateReport(result);
```

## API参考

详细的API文档请参考JavaDoc注释。

## 版本历史

- v1.0: 初始版本，支持四大类分层排序
- v1.1: 增加城市和时段特定权重配置
- v1.2: 优化归一化算法，增加监控功能

## 联系方式

如有问题请联系开发团队或提交Issue。
