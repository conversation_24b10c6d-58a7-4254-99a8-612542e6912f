package com.ctrip.dcs.infrastructure.factory;

import com.ctrip.dcs.domain.schedule.factory.SortFactory;
import com.ctrip.dcs.domain.schedule.sort.SortConfig;
import com.ctrip.dcs.domain.schedule.sort.Sorter;
import com.ctrip.dcs.domain.schedule.sort.feature.Feature;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.HierarchicalSorter;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.FeatureConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.HierarchicalSortConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.NormalizationType;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 分层排序工厂类
 * 负责创建和管理分层排序器
 * 
 * <AUTHOR> Assistant
 */
@Component
public class HierarchicalSortFactory {
    
    private static final Logger logger = LoggerFactory.getLogger(HierarchicalSortFactory.class);
    
    @Autowired
    private SortFactory traditionalSortFactory;
    
    private static final Map<String, Class<? extends Feature>> featureMap = new HashMap<>();
    
    static {
        // 初始化特征项映射
        loadFeatureClasses();
    }
    
    /**
     * 创建分层排序器
     * @param config 分层排序配置
     * @return 分层排序器
     */
    public HierarchicalSorter createHierarchicalSorter(HierarchicalSortConfig config) {
        try {
            // 验证配置
            if (!config.isValid()) {
                throw new IllegalArgumentException("Invalid hierarchical sort config: " + config.getConfigId());
            }
            
            // 创建传统特征项列表
            List<Feature> traditionalFeatures = createTraditionalFeatures(config);
            
            // 创建降级排序器
            Sorter fallbackSorter = createFallbackSorter(config);
            
            // 创建分层排序器
            HierarchicalSorter hierarchicalSorter = new HierarchicalSorter(
                    config, traditionalFeatures, fallbackSorter);
            
            logger.info("HierarchicalSortFactory", 
                    String.format("成功创建分层排序器: %s", config.getConfigId()));
            
            return hierarchicalSorter;
            
        } catch (Exception e) {
            logger.error("创建分层排序器失败", "configId: " + config.getConfigId(), e);
            throw new RuntimeException("Failed to create hierarchical sorter", e);
        }
    }
    
    /**
     * 创建默认的分层排序配置
     * @return 默认配置
     */
    public HierarchicalSortConfig createDefaultConfig() {
        // 创建大类权重配置
        Map<SortCategory, Double> categoryWeights = Map.of(
                SortCategory.TIME_SPACE_EFFICIENCY, 0.40,
                SortCategory.SERVICE_QUALITY, 0.30,
                SortCategory.ORDER_MATCHING, 0.20,
                SortCategory.GLOBAL_EFFICIENCY, 0.10
        );
        
        // 创建特征项配置
        Map<String, FeatureConfig> featureConfigs = createDefaultFeatureConfigs();
        
        return HierarchicalSortConfig.builder()
                .configId("default_hierarchical_sort")
                .configName("默认分层排序配置")
                .description("基于四大类的默认分层排序配置")
                .enabled(true)
                .categoryWeights(categoryWeights)
                .featureConfigs(featureConfigs)
                .build();
    }
    
    /**
     * 创建默认特征项配置
     */
    private Map<String, FeatureConfig> createDefaultFeatureConfigs() {
        Map<String, FeatureConfig> configs = new HashMap<>();
        
        // 时空效率类特征项
        configs.put("F9", FeatureConfig.builder()
                .featureId("F9")
                .featureName("局部时间间隔")
                .category(SortCategory.TIME_SPACE_EFFICIENCY)
                .weight(0.4)
                .normalizationType(NormalizationType.MIN_MAX)
                .enabled(true)
                .build());
                
        configs.put("F10", FeatureConfig.builder()
                .featureId("F10")
                .featureName("局部空驶距离")
                .category(SortCategory.TIME_SPACE_EFFICIENCY)
                .weight(0.4)
                .normalizationType(NormalizationType.INVERSE_MIN_MAX)
                .enabled(true)
                .build());
                
        configs.put("F4", FeatureConfig.builder()
                .featureId("F4")
                .featureName("接驾时间成本")
                .category(SortCategory.TIME_SPACE_EFFICIENCY)
                .weight(0.1)
                .normalizationType(NormalizationType.INVERSE_MIN_MAX)
                .enabled(true)
                .build());
                
        configs.put("F5", FeatureConfig.builder()
                .featureId("F5")
                .featureName("接驾距离成本")
                .category(SortCategory.TIME_SPACE_EFFICIENCY)
                .weight(0.1)
                .normalizationType(NormalizationType.INVERSE_MIN_MAX)
                .enabled(true)
                .build());
        
        // 服务质量类特征项
        configs.put("F2", FeatureConfig.builder()
                .featureId("F2")
                .featureName("司机分占比")
                .category(SortCategory.SERVICE_QUALITY)
                .weight(0.5)
                .normalizationType(NormalizationType.MIN_MAX)
                .enabled(true)
                .build());
                
        configs.put("F19", FeatureConfig.builder()
                .featureId("F19")
                .featureName("司机分层")
                .category(SortCategory.SERVICE_QUALITY)
                .weight(0.3)
                .normalizationType(NormalizationType.NONE)
                .enabled(true)
                .build());
                
        configs.put("F1", FeatureConfig.builder()
                .featureId("F1")
                .featureName("司机分")
                .category(SortCategory.SERVICE_QUALITY)
                .weight(0.2)
                .normalizationType(NormalizationType.MIN_MAX)
                .enabled(true)
                .build());
        
        // 订单匹配度类特征项
        configs.put("F14", FeatureConfig.builder()
                .featureId("F14")
                .featureName("订单里程价值")
                .category(SortCategory.ORDER_MATCHING)
                .weight(1.0)
                .normalizationType(NormalizationType.MIN_MAX)
                .enabled(true)
                .build());
        
        // 全局效率类特征项
        configs.put("F11", FeatureConfig.builder()
                .featureId("F11")
                .featureName("未来接单能力")
                .category(SortCategory.GLOBAL_EFFICIENCY)
                .weight(0.4)
                .normalizationType(NormalizationType.MIN_MAX)
                .enabled(true)
                .build());
                
        configs.put("F13", FeatureConfig.builder()
                .featureId("F13")
                .featureName("司机日收益")
                .category(SortCategory.GLOBAL_EFFICIENCY)
                .weight(0.6)
                .normalizationType(NormalizationType.MIN_MAX)
                .enabled(true)
                .build());
        
        return configs;
    }
    
    /**
     * 创建传统特征项列表
     */
    private List<Feature> createTraditionalFeatures(HierarchicalSortConfig config) {
        List<Feature> features = new ArrayList<>();
        
        for (String featureId : config.getFeatureConfigs().keySet()) {
            try {
                Class<? extends Feature> featureClass = featureMap.get(featureId);
                if (featureClass != null) {
                    Feature feature = featureClass.getDeclaredConstructor().newInstance();
                    features.add(feature);
                }
            } catch (Exception e) {
                logger.warn("创建特征项失败", "featureId: " + featureId, e);
            }
        }
        
        return features;
    }
    
    /**
     * 创建降级排序器
     */
    private Sorter createFallbackSorter(HierarchicalSortConfig config) {
        try {
            // 创建传统排序配置
            Map<String, Double> traditionalWeights = Map.of(
                    "F2", 1.0,
                    "F9", 0.8,
                    "F10", 0.8,
                    "F11", 0.4,
                    "F13", 0.75,
                    "F14", 1.0,
                    "F19", 1.0
            );
            
            SortConfig fallbackConfig = SortConfig.builder()
                    .sortId("fallback_sort")
                    .score("weight")
                    .features(traditionalWeights)
                    .build();
            
            return traditionalSortFactory.create(fallbackConfig);
            
        } catch (Exception e) {
            logger.warn("创建降级排序器失败", e);
            return null;
        }
    }
    
    /**
     * 加载特征项类
     */
    private static void loadFeatureClasses() {
        try {
            // 手动注册已知的特征项类，避免使用反射
            // 这样更加可靠，也避免了对reflections库的依赖

            // 注册现有的特征项类
            registerFeatureClass("F1", "com.ctrip.dcs.domain.schedule.sort.feature.impl.DriverPointFeature");
            registerFeatureClass("F2", "com.ctrip.dcs.domain.schedule.sort.feature.impl.DriverPointRatioFeature");
            registerFeatureClass("F4", "com.ctrip.dcs.domain.schedule.sort.feature.impl.DriverTakenTimeCostFeature");
            registerFeatureClass("F5", "com.ctrip.dcs.domain.schedule.sort.feature.impl.DriverTakenTimeRangeFeature");
            registerFeatureClass("F9", "com.ctrip.dcs.domain.schedule.sort.feature.impl.DriverLocalTimeIntervalFeature");
            registerFeatureClass("F10", "com.ctrip.dcs.domain.schedule.sort.feature.impl.DriverLocalEmptyDistanceFeature");
            registerFeatureClass("F11", "com.ctrip.dcs.domain.schedule.sort.feature.impl.FutureOrderNumEffectFeature");
            registerFeatureClass("F13", "com.ctrip.dcs.domain.schedule.sort.feature.impl.DriverDayProfitFeature");
            registerFeatureClass("F14", "com.ctrip.dcs.domain.schedule.sort.feature.impl.OrderMileageValueFeature");
            registerFeatureClass("F19", "com.ctrip.dcs.domain.schedule.sort.feature.impl.GradientDriverNewFeature");

            logger.info("HierarchicalSortFactory",
                    String.format("加载特征项类完成，共 %d 个", featureMap.size()));

        } catch (Exception e) {
            logger.error("加载特征项类失败", e);
        }
    }

    /**
     * 注册特征项类
     */
    private static void registerFeatureClass(String featureId, String className) {
        try {
            @SuppressWarnings("unchecked")
            Class<? extends Feature> featureClass = (Class<? extends Feature>) Class.forName(className);
            featureMap.put(featureId, featureClass);
        } catch (ClassNotFoundException e) {
            logger.warn("特征项类不存在", "featureId: " + featureId + ", className: " + className);
        } catch (Exception e) {
            logger.warn("注册特征项类失败", "featureId: " + featureId, e);
        }
    }
    
    /**
     * 获取支持的特征项列表
     */
    public Set<String> getSupportedFeatures() {
        return featureMap.keySet();
    }
    
    /**
     * 验证配置的有效性
     */
    public boolean validateConfig(HierarchicalSortConfig config) {
        try {
            // 检查基本配置
            if (!config.isValid()) {
                return false;
            }
            
            // 检查特征项是否都支持
            for (String featureId : config.getFeatureConfigs().keySet()) {
                if (!featureMap.containsKey(featureId)) {
                    logger.warn("不支持的特征项", "featureId: " + featureId);
                    return false;
                }
            }
            
            return true;
            
        } catch (Exception e) {
            logger.error("配置验证失败", e);
            return false;
        }
    }
}
