package com.ctrip.dcs.infrastructure.service;

import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.constants.MetricsTagConstants;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.command.DspSortCommand;
import com.ctrip.dcs.domain.schedule.context.DspContext;
import com.ctrip.dcs.domain.schedule.factory.SortFactory;
import com.ctrip.dcs.domain.schedule.service.DspContextService;
import com.ctrip.dcs.domain.schedule.service.SortService;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.Sorter;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.base.Stopwatch;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

;

/**
 * <AUTHOR>
 */
@Component
public class SortServiceImpl implements SortService {

    private static final Logger logger = LoggerFactory.getLogger(SortServiceImpl.class);

    @Autowired
    private DspContextService dspContextService;

    @Autowired
    private SortFactory sortFactory;

    @Autowired
    @Qualifier("sortConfig")
    private ConfigService sortConfig;

    @Override
    public List<SortModel> sort(DspSortCommand command) {
        List<SortModel> sortModels = command.getDrivers().stream()
                .filter(Objects::nonNull)
                .map(driver -> new SortModel(new DspModelVO(command.getDspOrder(), driver)))
                .collect(Collectors.toList());
        logger.info("RecommendService_sort", "sort size: : " + sortModels.size(), Map.of("dspOrderId", Optional.ofNullable(command).map(DspSortCommand::getDspOrder).map(DspOrderVO::getDspOrderId).orElse("")));
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            // 创建排序
            Sorter sorter = sortFactory.create(command.getSubSku().getSort());
            // 排序
            sorter.sort(sortModels, new SortContext(command.getDspOrder(), command.getSubSku(), new DspContext(dspContextService), sortConfig));
        } finally {
            Map<String, String> tags = Maps.newHashMap();
            tags.put(MetricsTagConstants.SUB_SKU_ID, Optional.ofNullable(command.getSubSku()).map(SubSkuVO::getSubSkuId).orElse(0).toString());
            MetricsUtil.recordTime(MetricsConstants.PROCESS_SORT_CHAIN_TIME_KEY, stopwatch.elapsed(TimeUnit.MILLISECONDS), tags);
        }
        return sortModels;
    }

    @Override
    public List<DriverVO> sort(DspOrderVO order, SubSkuVO subSku, List<DriverVO> drivers) {
        List<SortModel> sortModels = sort(new DspSortCommand(order, subSku, drivers));
        return sortModels.stream().map(SortModel::getModel).map(DspModelVO::getDriver).collect(Collectors.toList());
    }
}
